<template>
  <div class="min-h-screen flex flex-col bg-white">
    <NavbarStudent />
    <Sidebar :is-open="isSidebarOpen" @toggle="toggleSidebar" />

    <!-- Main content wrapper with responsive adjustments -->
    <div
      class="w-full flex-grow transition-all duration-300 ease-in-out overflow-x-hidden"
      :class="[
        contentClass,
        {
          'md:ml-64': isSidebarOpen,
          'ml-0': !isSidebarOpen,
          'px-4 sm:px-6 md:px-8': !noPadding,
          'py-4 sm:py-6': !noPadding && !noPaddingY,
          'pl-10 sm:pl-12': !isSidebarOpen && !noPadding
        }
      ]"
    >
      <!-- Inner container to ensure proper content flow -->
      <div class="w-full max-w-full">
        <slot></slot>
      </div>
    </div>

    <FooterStudent />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import NavbarStudent from '@/components/@student/NavbarStudent.vue';
import FooterStudent from '@/components/@student/FooterStudent.vue';
import Sidebar from '@/components/@student/Sidebar.vue';
import { isSidebarOpen, toggleSidebar } from '@/data/sidebarState';

// Props to customize the layout
const props = defineProps({
  // Additional classes for the content wrapper
  contentClass: {
    type: String,
    default: ''
  },
  // Disable padding
  noPadding: {
    type: Boolean,
    default: false
  },
  // Disable vertical padding only
  noPaddingY: {
    type: Boolean,
    default: false
  }
});
</script>
