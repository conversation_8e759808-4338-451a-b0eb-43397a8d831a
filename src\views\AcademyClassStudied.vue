<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <!-- Navigation Tabs -->
    <div class="flex items-center justify-center sm:justify-start space-x-4 sm:space-x-8 mb-4">
        <div
          v-for="tab in ['studied', 'completed']"
          :key="tab"
          class="cursor-pointer"
          @click="activeTab = tab"
        >
          <span
            class="inline-block px-4 sm:px-5 py-2 sm:py-2.5 text-sm sm:text-[15px] whitespace-nowrap"
            :class="[
              activeTab === tab
                ? 'border border-orange text-orange rounded-full bg-white'
                : 'text-gray-400 hover:text-gray-600'
            ]"
          >
            {{ tab === 'studied' ? 'Classes Studied' : 'Completed Classes' }}
          </span>
        </div>
    </div>

    <!-- Main Content -->
      <!-- Classes Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 relative">
        <template v-if="filteredClasses.length > 0">
            <ClassCard
              v-for="classItem in visibleClasses"
              :key="classItem.id"
              :class-data="classItem"
              @view-class="handleViewClass"
            />
        </template>
        <div v-else class="col-span-1 md:col-span-2 text-center py-8 sm:py-12">
          <div class="text-gray-500 text-sm sm:text-base">
            No {{ activeTab === 'studied' ? 'ongoing' : 'completed' }} classes found.
          </div>
        </div>
      </div>

      <!-- Loading Indicator - hanya muncul setelah 4 card pertama -->
      <div
        v-if="visibleClasses.length >= INITIAL_ITEMS && hasMoreClasses"
        ref="observerTarget"
        class="py-4 sm:py-8 mt-2 sm:mt-4 text-center"
      >
        <div v-if="loadingMore" class="flex justify-center items-center">
          <div class="w-6 h-6 sm:w-8 sm:h-8 border-3 sm:border-4 border-orange border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import ClassCard from '@/components/@student/ClassCard.vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import { useClassStore, STORAGE_KEYS } from '@/data/availableClasses';

const router = useRouter();
const route = useRoute();
const classStore = useClassStore();

// Initialize activeTab from localStorage or default to 'studied'
const activeTab = ref(localStorage.getItem(STORAGE_KEYS.ACTIVE_TAB) || 'studied');

// Watch for query parameter changes to update the active tab
watch(() => route.query.tab, (newTab) => {
  if (newTab === 'completed' || newTab === 'studied') {
    activeTab.value = newTab;
  }
}, { immediate: true });

// Constants for pagination
const INITIAL_ITEMS = 4;
const ITEMS_PER_PAGE = 4;
const currentPage = ref(1);
const loadingMore = ref(false);
const observerTarget = ref(null);
let observer = null;

// Watch activeTab changes and save to localStorage
watch(activeTab, (newTab) => {
  localStorage.setItem(STORAGE_KEYS.ACTIVE_TAB, newTab);
});

const filteredClasses = computed(() => {
  const classes = classStore.getClassesByStatus(activeTab.value);

  // Sort based on active tab
  if (activeTab.value === 'studied') {
    // Sort by posted date (newest first)
    return [...classes].sort((a, b) => {
      const dateA = new Date(`${a.postedDate}T${a.postedTime}`);
      const dateB = new Date(`${b.postedDate}T${b.postedTime}`);
      return dateB - dateA; // Newest first
    });
  } else if (activeTab.value === 'completed') {
    // Sort by completion date (earliest completed first)
    return [...classes].sort((a, b) => {
      // Find the earliest material that was completed for each class
      // This is a proxy for when the class was completed
      const getEarliestCompletionDate = (classItem) => {
        // Find materials with 'reviewed' status as they represent completed items
        const completedMaterials = classItem.materials.filter(m =>
          m.hasTask && ['reviewed', 'turned_in'].includes(m.taskStatus) && m.submissionDate
        );

        if (completedMaterials.length === 0) {
          // Fallback to posted date if no completed materials
          return new Date(`${classItem.postedDate}T${classItem.postedTime}`);
        }

        // Sort materials by submission date and get the earliest
        return completedMaterials
          .map(m => new Date(`${m.submissionDate}T${m.submissionTime || '00:00'}`))
          .sort((a, b) => a - b)[0];
      };

      const completionDateA = getEarliestCompletionDate(a);
      const completionDateB = getEarliestCompletionDate(b);

      return completionDateA - completionDateB; // Earliest first
    });
  }

  return classes;
});

const visibleClasses = computed(() => {
  const endIndex = INITIAL_ITEMS + (currentPage.value - 1) * ITEMS_PER_PAGE;
  return filteredClasses.value.slice(0, endIndex);
});

const hasMoreClasses = computed(() => {
  return visibleClasses.value.length < filteredClasses.value.length;
});

const handleViewClass = (classId) => {
  console.log('=== VIEWING CLASS FROM ACADEMY PAGE ===');
  console.log('Class ID:', classId);
  console.log('Active tab:', activeTab.value);

  // Set current class
  const success = classStore.setCurrentClass(classId);
  console.log('Setting current class successful:', success);

  // Get the class object to determine its status
  const classObj = classStore.getClassById(classId);

  if (classObj) {
    console.log('Class title:', classObj.title);
    console.log('Class status:', classObj.status);
    console.log('Materials count:', classObj.materials?.length || 0);

    // Store the source tab (studied or completed) in localStorage
    localStorage.setItem(STORAGE_KEYS.CLASS_SOURCE, activeTab.value);
    console.log('Class source set to', activeTab.value, 'in localStorage');

    // Calculate and log progress
    const progress = classStore.calculateProgress(classId);
    console.log('Class progress:', progress + '%');
  } else {
    console.error('Failed to get class data!');
  }

  // Navigate to the class detail page with reload parameter to ensure fresh state
  router.push({
    name: 'DetailClass',
    params: { classId },
    query: { reload: Date.now() } // Add timestamp to force reload
  });
};

const loadMoreClasses = () => {
  if (!loadingMore.value && hasMoreClasses.value) {
    loadingMore.value = true;
    // Simulasi loading selama 300ms untuk menunjukkan loading indicator
    setTimeout(() => {
      currentPage.value++;
      loadingMore.value = false;
    }, 300);
  }
};

const setupIntersectionObserver = () => {
  // Cleanup existing observer
  if (observer) {
    observer.disconnect();
  }

  if (observerTarget.value) {
    observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMoreClasses.value) {
          loadMoreClasses();
        }
      },
      { threshold: 0.5 }
    );
    observer.observe(observerTarget.value);
  }
};

watch(activeTab, () => {
  currentPage.value = 1;
  // Reset observer when tab changes
  nextTick(() => {
    setupIntersectionObserver();
  });
});

onMounted(() => {
  setupIntersectionObserver();
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
});
</script>