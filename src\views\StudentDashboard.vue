<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <h1 class="text-2xl font-bold mb-6">Student Dashboard</h1>

      <!-- Dashboard Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <!-- Class Complete Card -->
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div class="flex justify-between items-center">
            <div>
              <p class="text-gray-500 text-sm">Class Complete</p>
              <h2 class="text-3xl font-bold">{{ completedClassesCount }}</h2>
            </div>
            <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Total Student Card -->
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div class="flex justify-between items-center">
            <div>
              <p class="text-gray-500 text-sm">Total Student</p>
              <h2 class="text-3xl font-bold">{{ totalStudents }}</h2>
            </div>
            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Total Classes Card -->
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div class="flex justify-between items-center">
            <div>
              <p class="text-gray-500 text-sm">Total Classes</p>
              <h2 class="text-3xl font-bold">{{ totalClasses }}</h2>
            </div>
            <div class="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Lesson Complete Card -->
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div class="flex justify-between items-center">
            <div>
              <p class="text-gray-500 text-sm">Lesson Complete</p>
              <h2 class="text-3xl font-bold">{{ completedLessonsCount }}</h2>
            </div>
            <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Class Taken Times & Test Results -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Class Taken Times Chart -->
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <div class="flex justify-between items-center mb-4">
            <h3 class="font-semibold">Class Taken Times</h3>
            <div class="bg-gray-100 rounded-md px-2 py-1 text-xs">
              Monthly
            </div>
          </div>
          <p class="text-xs text-gray-500 mb-4">Taken records of last Years</p>

          <div class="flex space-x-2 mb-2">
            <button class="px-3 py-1 rounded-full text-xs bg-orange text-white">Active Classes</button>
            <button class="px-3 py-1 rounded-full text-xs bg-purple-600 text-white">Active Students</button>
          </div>

          <!-- Chart Implementation -->
          <div class="h-64 w-full relative">
            <canvas id="classActivityChart" class="w-full h-full"></canvas>
          </div>

          <!-- Chart Legend -->
          <div class="flex space-x-4 mt-4 text-xs">
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full bg-orange mr-1"></div>
              <span @click="toggleChartDataset(0)" class="cursor-pointer hover:underline">Active Classes</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full bg-purple-600 mr-1"></div>
              <span @click="toggleChartDataset(1)" class="cursor-pointer hover:underline">Active Students</span>
            </div>
          </div>
        </div>

        <!-- Test Results -->
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <h3 class="font-semibold mb-4">Browse test results</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead>
                <tr class="text-left text-xs text-gray-500">
                  <th class="pb-2">Name</th>
                  <th class="pb-2">Total score</th>
                  <th class="pb-2">Start Date</th>
                  <th class="pb-2">Time</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(result, index) in sortedTestResults" :key="index" class="border-t border-gray-100">
                  <td class="py-3 flex items-center">
                    <img :src="result.avatar" alt="Student avatar" class="w-8 h-8 rounded-full mr-2">
                    {{ result.name }}
                  </td>
                  <td class="py-3">
                    <span :class="getScoreColorClass(result.score)">{{ result.score }}%</span>
                  </td>
                  <td class="py-3">{{ result.date }}</td>
                  <td class="py-3">{{ result.time }}</td>
                </tr>
              </tbody>
            </table>
            <div v-if="showLoadMore" class="mt-4 text-center">
              <button @click="loadMoreResults" class="px-4 py-2 bg-orange text-white rounded-md hover:bg-orange-600 transition-colors">
                Load More
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- All Exams Section -->
      <div class="mb-8">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold">All Exams</h3>
          <div class="flex space-x-2">
            <div class="relative">
              <input v-model="searchQuery" type="text" placeholder="Search..." class="pl-8 pr-4 py-1.5 rounded-md border border-gray-300 text-sm focus:ring-orange focus:border-orange">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 absolute left-2.5 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <button class="px-3 py-1.5 rounded-md border border-gray-300 text-sm flex items-center">
              Exam status
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <button class="px-3 py-1.5 rounded-md bg-orange text-white text-sm">
              Add new category
            </button>
          </div>
        </div>

        <!-- Exam Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          <div v-for="(exam, index) in filteredExams" :key="index" class="bg-white rounded-lg overflow-hidden shadow-sm">
            <div class="h-40 bg-cover bg-center" :style="{ backgroundImage: exam.image ? `url(${exam.image})` : exam.gradient }"></div>
            <div class="p-4">
              <div class="flex justify-between items-center mb-2">
                <h4 class="font-semibold line-clamp-1">{{ exam.title }}</h4>
                <span :class="{
                  'px-2 py-0.5 rounded-full text-xs': true,
                  'bg-green-100 text-green-600': exam.status === 'completed',
                  'bg-orange-100 text-orange': exam.status === 'Aktif',
                  'bg-red-100 text-red-600': exam.status === 'past_due',
                  'bg-yellow-100 text-yellow-600': exam.status === 'pending'
                }">{{ exam.status }}</span>
              </div>
              <div class="flex flex-wrap gap-2 text-xs text-gray-500 mb-2">
                <span class="px-2 py-1 bg-gray-100 rounded-full">{{ exam.category }}</span>
                <span v-if="exam.questions" class="px-2 py-1 bg-gray-100 rounded-full">{{ exam.questions }}</span>
                <span v-if="exam.points" class="px-2 py-1 bg-gray-100 rounded-full">{{ exam.points }}</span>
              </div>
              <p v-if="exam.description" class="text-sm text-gray-600 mb-3 line-clamp-2">{{ exam.description }}</p>
              <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span>Created on: {{ exam.createdDate }}</span>
                </div>
                <div v-if="exam.dueDate" class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Due date: {{ exam.dueDate }}</span>
                </div>
              </div>
              <button class="w-full py-1.5 text-center text-sm text-orange border border-orange rounded-md hover:bg-orange hover:text-white transition-colors">
                See detail
              </button>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-center items-center space-x-2">
          <button
            @click="prevPage"
            :disabled="currentPage === 1"
            :class="{
              'px-3 py-1 rounded-md text-sm': true,
              'bg-orange text-white': currentPage !== 1,
              'bg-gray-100 text-gray-400 cursor-not-allowed': currentPage === 1
            }">
            Sebelumnya
          </button>

          <div class="flex space-x-1">
            <button
              v-for="page in totalPages"
              :key="page"
              @click="goToPage(page)"
              :class="{
                'px-3 py-1 rounded-md text-sm': true,
                'bg-orange text-white': currentPage === page,
                'bg-gray-100 text-gray-600 hover:bg-gray-200': currentPage !== page
              }">
              {{ page }}
            </button>
          </div>

          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            :class="{
              'px-3 py-1 rounded-md text-sm': true,
              'bg-orange text-white': currentPage !== totalPages,
              'bg-gray-100 text-gray-400 cursor-not-allowed': currentPage === totalPages
            }">
            Selanjutnya
          </button>
        </div>
      </div>

  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import { useClassStore } from '@/data/availableClasses';
import Chart from 'chart.js/auto';

const classStore = useClassStore();

// Dashboard statistics
const completedClassesCount = ref(0);
const totalStudents = ref('0');
const totalClasses = ref(0);
const completedLessonsCount = ref(0);

// Computed properties for class data
const classes = computed(() => {
  return classStore.classes?.value || [];
});

const completedClasses = computed(() => {
  return classes.value.filter(c => c.progress === 100);
});

const studiedClasses = computed(() => {
  return classes.value.filter(c => c.status === 'studied');
});

// Count total lessons/materials across all classes
const totalLessons = computed(() => {
  return classes.value.reduce((total, cls) => {
    return total + (cls.materials?.length || 0);
  }, 0);
});

// Count completed lessons/materials across all classes
const completedLessons = computed(() => {
  return classes.value.reduce((total, cls) => {
    const completedMaterials = cls.materials?.filter(m => m.isComplete || (m.isRead && (!m.hasTask || (m.hasTask && m.taskStatus && m.taskStatus !== 'pending' && m.taskStatus !== 'past_due')))) || [];
    return total + completedMaterials.length;
  }, 0);
});

// Monthly class activity data for chart - generate based on class data
const monthlyClassData = computed(() => {
  const currentYear = new Date().getFullYear();
  const monthlyData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    activeClasses: Array(12).fill(0),
    activeStudents: Array(12).fill(0)
  };

  console.log('Calculating monthly class data...');
  console.log('Total classes:', classes.value.length);

  // Count active classes and students by month
  classes.value.forEach(cls => {
    // Use joinedDate instead of startDate (which doesn't exist in the data)
    const dateToUse = cls.joinedDate || cls.postedDate;

    if (dateToUse) {
      const classDate = new Date(dateToUse);
      const month = classDate.getMonth();

      console.log(`Class: ${cls.title}, Date: ${dateToUse}, Month: ${month}, Status: ${cls.status}, Progress: ${cls.progress}`);

      // Count active classes - include all classes that aren't completed
      if (cls.status === 'active' || cls.status === 'ongoing' ||
          cls.status === 'available' || (cls.status === 'studied' && cls.progress < 100)) {

        monthlyData.activeClasses[month] += 1;
        console.log(`Added to active classes for month ${month}`);

        // Count active students
        const activeStudents = cls.studentsEnrolled || 0;
        console.log(`Students enrolled: ${activeStudents}`);

        // Calculate active students based on class progress and material completion
        let activeRatio = 1;
        if (cls.materials && cls.materials.length > 0) {
          const completedMaterials = cls.materials.filter(m =>
            m.isComplete || (m.isRead && m.taskStatus && m.taskStatus !== 'pending' && m.taskStatus !== 'past_due')
          ).length;
          activeRatio = Math.max(completedMaterials / cls.materials.length, 0.1);
          console.log(`Materials: ${cls.materials.length}, Completed: ${completedMaterials}, Ratio: ${activeRatio}`);
        }

        // Use progress as an activity indicator with a minimum threshold
        const progressRatio = Math.max((cls.progress || 10) / 100, 0.1);
        const estimatedActiveStudents = Math.round(activeStudents * progressRatio * activeRatio);

        console.log(`Progress ratio: ${progressRatio}, Estimated active students: ${estimatedActiveStudents}`);
        monthlyData.activeStudents[month] += estimatedActiveStudents;
      }
    } else {
      console.log(`Class ${cls.title} has no date information`);
    }
  });

  console.log('Monthly data calculated:', monthlyData);
  return monthlyData;
});

// Chart instance reference
const chartInstance = ref(null);

// Test results data - generate from class materials with tasks
const testResults = computed(() => {
  const results = [];

  // Extract test results from class materials with tasks
  classes.value.forEach(cls => {
    if (cls.materials) {
      cls.materials.forEach(material => {
        if (material.hasTask && material.taskStatus) {
          const score = material.taskScore || 0;

          results.push({
            name: material.studentName || 'Student',
            avatar: material.studentAvatar || '/prof.png',
            score: parseFloat(score.toFixed(1)),
            date: material.submissionDate || material.postedDate,
            time: material.submissionTime || material.postedTime
          });
        }
      });
    }
  });

  return results;
});

// Jumlah test results yang ditampilkan
const displayedResultsCount = ref(5);
const showLoadMore = computed(() => testResults.value.length > displayedResultsCount.value);

// Sort test results by score (highest first) dan terapkan limit
const sortedTestResults = computed(() => {
  return [...testResults.value]
    .sort((a, b) => b.score - a.score)
    .slice(0, displayedResultsCount.value);
});

// Function untuk load more test results
const loadMoreResults = () => {
  displayedResultsCount.value += 5;
};

// Exam data with search functionality and pagination
const currentPage = ref(1);
const itemsPerPage = 6;

const exams = computed(() => {
  const examsList = [];

  // Generate exams from class materials with tasks
  classes.value.forEach(cls => {
    if (cls.materials) {
      cls.materials.forEach(material => {
        if (material.hasTask) {
          examsList.push({
            title: material.title || `${cls.title} - Tugas`,
            status: material.taskStatus || 'Aktif',
            category: cls.category || 'Umum',
            questions: material.taskQuestions ? `${material.taskQuestions} Soal` : 'Tugas Praktik',
            points: material.taskPoints ? `Total Nilai: ${material.taskPoints}` : null,
            createdDate: material.postedDate,
            dueDate: material.dueDate,
            description: material.description,
            image: material.imageUrl || cls.imageUrl
          });
        }
      });
    }
  });

  return examsList;
});

// Search functionality for exams
const searchQuery = ref('');
const processedExams = computed(() => {
  return exams.value.map((exam, index) => {
    if (!exam.image || exam.image.includes('images/exam') || exam.image === '') {
      // Use different background colors for each exam card
      const images = [
        'linear-gradient(to right, #c471f5, #fa71cd)',
        'linear-gradient(to right, #4facfe, #00f2fe)',
        'linear-gradient(to right, #ff8177, #ff867a)'
      ];
      return { ...exam, image: images[index % images.length] };
    }
    return exam;
  });
});
const filteredExams = computed(() => {
  const query = searchQuery.value.toLowerCase();
  const filtered = processedExams.value.filter(exam =>
    exam.title.toLowerCase().includes(query) ||
    exam.category.toLowerCase().includes(query)
  );

  // Calculate total pages
  const totalPages = Math.ceil(filtered.length / itemsPerPage);
  if (currentPage.value > totalPages) {
    currentPage.value = Math.max(1, totalPages);
  }

  // Get paginated results
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  return filtered.slice(startIndex, startIndex + itemsPerPage);
});

// Pagination controls
const totalPages = computed(() => {
  const filtered = processedExams.value.filter(exam => {
    const query = searchQuery.value.toLowerCase();
    return exam.title.toLowerCase().includes(query) ||
           exam.category.toLowerCase().includes(query);
  });
  return Math.ceil(filtered.length / itemsPerPage);
});

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const goToPage = (page) => {
  currentPage.value = page;
};

// Function to get color class based on score
const getScoreColorClass = (score) => {
  if (score >= 18) return 'text-orange';
  if (score >= 15) return 'text-blue-500';
  return 'text-yellow-500';
};

// Function to toggle chart dataset visibility
const toggleChartDataset = (datasetIndex) => {
  if (chartInstance.value) {
    const meta = chartInstance.value.getDatasetMeta(datasetIndex);
    meta.hidden = !meta.hidden;
    chartInstance.value.update();
  }
};

// Function to initialize chart
const initializeChart = () => {
  const chartElement = document.getElementById('classActivityChart');
  if (!chartElement) {
    console.error('Chart element not found!');
    return;
  }

  console.log('Initializing chart...');
  console.log('Chart data - Active Classes:', monthlyClassData.value.activeClasses);
  console.log('Chart data - Active Students:', monthlyClassData.value.activeStudents);

  // Ensure we have some data to display
  const hasActiveClassData = monthlyClassData.value.activeClasses.some(value => value > 0);
  const hasActiveStudentData = monthlyClassData.value.activeStudents.some(value => value > 0);

  if (!hasActiveClassData && !hasActiveStudentData) {
    console.warn('No data available for chart. Adding sample data for visualization.');
    // Add sample data if no real data is available (for testing purposes)
    monthlyClassData.value.activeClasses = [2, 3, 5, 4, 6, 8, 7, 9, 8, 10, 9, 11];
    monthlyClassData.value.activeStudents = [10, 15, 25, 20, 30, 40, 35, 45, 40, 50, 45, 55];
  }

  // Destroy existing chart if it exists
  if (chartInstance.value) {
    console.log('Destroying existing chart instance');
    chartInstance.value.destroy();
  }

  const ctx = chartElement.getContext('2d');
  chartInstance.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels: monthlyClassData.value.labels,
      datasets: [
        {
          label: 'Active Classes',
          data: monthlyClassData.value.activeClasses,
          borderColor: '#F2720C', // Orange color
          backgroundColor: 'rgba(242, 114, 12, 0.1)',
          tension: 0.4,
          fill: true,
          borderWidth: 2,
          pointBackgroundColor: '#F2720C',
          pointRadius: 3,
          pointHoverRadius: 5
        },
        {
          label: 'Active Students',
          data: monthlyClassData.value.activeStudents,
          borderColor: '#9333ea', // Purple color
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          tension: 0.4,
          fill: true,
          borderWidth: 2,
          pointBackgroundColor: '#9333ea',
          pointRadius: 3,
          pointHoverRadius: 5
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            title: function(tooltipItems) {
              return tooltipItems[0].label + ' ' + new Date().getFullYear();
            },
            label: function(context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                label += context.parsed.y;
              }
              return label;
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.05)'
          },
          ticks: {
            precision: 0, // Show only integer values
            font: {
              size: 10
            }
          }
        },
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 10
            }
          }
        }
      },
      interaction: {
        mode: 'index',
        intersect: false
      }
    }
  });
};

// This function is already defined above

onMounted(() => {
  // Update statistics based on actual data
  completedClassesCount.value = completedClasses.value.length;
  totalClasses.value = classes.value.length;
  completedLessonsCount.value = completedLessons.value;
  totalStudents.value = classes.value.reduce((total, cls) => total + cls.studentsEnrolled, 0).toLocaleString();

  // Initialize chart after DOM is ready
  setTimeout(() => {
    initializeChart();
  }, 100);
});

// This function is already defined above as processedExams

// Watch for changes in class data and update statistics
watch([classes, completedClasses, completedLessons], () => {
  console.log('Class data changed, updating statistics and chart...');

  completedClassesCount.value = completedClasses.value.length;
  totalClasses.value = classes.value.length;
  completedLessonsCount.value = completedLessons.value;
  totalStudents.value = classes.value.reduce((total, cls) => total + cls.studentsEnrolled, 0).toLocaleString();

  console.log('Updated statistics:', {
    completedClasses: completedClassesCount.value,
    totalClasses: totalClasses.value,
    completedLessons: completedLessonsCount.value,
    totalStudents: totalStudents.value
  });

  // Update chart when class data changes
  if (chartInstance.value) {
    console.log('Updating chart with new data...');
    console.log('New active classes data:', monthlyClassData.value.activeClasses);
    console.log('New active students data:', monthlyClassData.value.activeStudents);

    chartInstance.value.data.datasets[0].data = monthlyClassData.value.activeClasses;
    chartInstance.value.data.datasets[1].data = monthlyClassData.value.activeStudents;
    chartInstance.value.update();
    console.log('Chart updated successfully');
  } else {
    console.warn('Chart instance not available, initializing new chart...');
    setTimeout(() => {
      initializeChart();
    }, 100);
  }
});
</script>