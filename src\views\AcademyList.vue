<template>
  <div>
    <!-- Navbar -->
    <NavbarAcademy />

    <div class="px-8 py-10">
      <!-- Tabs -->
      <div class="flex gap-10 border-b border-gray-300 mb-6 text-sm font-medium">
        <button
          :class="activeTab === 'assignment' ? 'border-b-2 border-[#F2720C] text-black' : 'text-gray-500'"
          @click="activeTab = 'assignment'; showAssignmentDetail = false"
        >
          Assignment
        </button>
        <button
          :class="activeTab === 'student' ? 'border-b-2 border-[#F2720C] text-black' : 'text-gray-500'"
          @click="activeTab = 'student'; showAssignmentDetail = false"
        >
          Student
        </button>
      </div>

      <!-- Assignment Tab -->
      <div v-if="activeTab === 'assignment'">
        <template v-if="!showAssignmentDetail">
          <h2 class="text-xl font-semibold mb-4">Assignment</h2>
          <!-- Class Dropdown -->
          <select
            v-model="selectedClass"
            class="w-full mb-6 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm"
          >
            <option value="">Class</option>
            <option v-for="classItem in classList" :key="classItem" :value="classItem">
              {{ classItem }}
            </option>
          </select>

          <!-- Assignment List -->
          <div v-for="(item, idx) in sortedAssignments" :key="idx" class="border-b py-3 flex items-center justify-between cursor-pointer hover:bg-gray-50" @click="openAssignmentDetail(item)">
            <div class="flex items-center gap-2 text-[#911c1c] font-medium text-sm">
              <img src="/upfile.png" class="w-5 h-5" />
              {{ item.title }}
            </div>
            <span class="text-xl text-gray-600">&gt;</span>
          </div>
        </template>
        <template v-else>
          <!-- Breadcrumbs -->
          <nav class="flex items-center text-xs text-gray-400 mb-6 gap-2">
            <button class="hover:underline" @click="showAssignmentDetail = false">Assignment</button>
            <span>&gt;</span>
            <button class="hover:underline" @click="filterAssignmentByClass(selectedAssignment.class)">{{ selectedAssignment.class || 'Mastery In Golang: From Zero to Hero' }}</button>
            <span>&gt;</span>
            <span class="text-black font-semibold">{{ selectedAssignment.title }}</span>
          </nav>
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-2xl font-semibold">Task Students Submission</h2>
            <!-- Dropdown Sort By -->
            <div class="relative inline-block" ref="el => sortDropdownRef = el">
              <button @click="showSortDropdown = !showSortDropdown" class="flex items-center gap-2 bg-[#F2720C] text-white px-4 py-2 rounded-lg font-semibold hover:bg-[#d65e00] transition">
                Sort By
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7h18M6 12h12M9 17h6"></path></svg>
              </button>
              <div v-if="showSortDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded shadow-lg z-20 py-2 text-sm">
                <button @click="setSort('az')" :class="sortBy === 'az' ? 'bg-gray-100' : ''" class="block w-full text-left px-4 py-2 hover:bg-gray-100">Student Name (A-Z)</button>
                <button @click="setSort('za')" :class="sortBy === 'za' ? 'bg-gray-100' : ''" class="block w-full text-left px-4 py-2 hover:bg-gray-100">Student Name (Z-A)</button>
                <button disabled class="block w-full text-left px-4 py-2 text-gray-400 bg-gray-50 cursor-not-allowed">On time</button>
                <button @click="setSort('overdue')" :class="sortBy === 'overdue' ? 'bg-gray-100' : ''" class="block w-full text-left px-4 py-2 hover:bg-gray-100">Overdue</button>
                <button @click="setSort('graded')" :class="sortBy === 'graded' ? 'bg-gray-100' : ''" class="block w-full text-left px-4 py-2 hover:bg-gray-100">Graded</button>
                <button @click="setSort('notgraded')" :class="sortBy === 'notgraded' ? 'bg-gray-100' : ''" class="block w-full text-left px-4 py-2 hover:bg-gray-100">Not Graded</button>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full text-sm border border-gray-200 rounded-md overflow-hidden">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-4 py-2 text-left font-medium text-gray-600">Student</th>
                  <th class="px-4 py-2 text-left font-medium text-gray-600">Submission Date</th>
                  <th class="px-4 py-2 text-left font-medium text-gray-600">Status</th>
                  <th class="px-4 py-2 text-left font-medium text-gray-600">Grading Date</th>
                  <th class="px-4 py-2 text-left font-medium text-gray-600">Grades</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(student, idx) in paginatedAssignmentStudents" :key="idx" class="border-t cursor-pointer hover:bg-gray-50" @click="openReviewModal(student)">
                  <td class="px-4 py-3 flex items-center gap-2">
                    <img :src="student.image" class="w-8 h-8 rounded-full object-cover" />
                    <span class="truncate max-w-[120px]">{{ student.name }}</span>
                  </td>
                  <td class="px-4 py-3">{{ student.submissionDate }}</td>
                  <td class="px-4 py-3">
                    <span :class="student.status === 'on time' || student.status === 'ontime' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'" class="px-3 py-1 rounded-full text-xs font-semibold">
                      {{ student.status }}
                    </span>
                  </td>
                  <td class="px-4 py-3">{{ student.gradingDate }}</td>
                  <td class="px-4 py-3">{{ student.grade ?? '-' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- Pagination Controls -->
          <div class="flex flex-col md:flex-row items-center justify-between mt-6 gap-4">
            <div class="flex items-center gap-2">
              <span>Show</span>
              <select v-model.number="assignmentRowsPerPage" class="border rounded px-2 py-1">
                <option v-for="opt in [10, 20, 50]" :key="opt" :value="opt">{{ opt }}</option>
              </select>
              <span>Row</span>
            </div>
            <div class="flex items-center gap-1">
              <button :disabled="assignmentCurrentPage === 1" @click="goToAssignmentPage(assignmentCurrentPage - 1)" class="px-2 py-1 rounded bg-gray-100 text-gray-400 disabled:opacity-50">
                &lt;
              </button>
              <template v-for="page in assignmentVisiblePages">
                <button
                  v-if="page !== '...'"
                  :key="page"
                  @click="goToAssignmentPage(page)"
                  :class="[assignmentCurrentPage === page ? 'bg-[#F2720C] text-white' : 'bg-white text-gray-800', 'px-3 py-1 rounded font-semibold transition']"
                >
                  {{ page }}
                </button>
                <span v-else :key="'ellipsis-' + page" class="px-2">...</span>
              </template>
              <button :disabled="assignmentCurrentPage === assignmentTotalPages" @click="goToAssignmentPage(assignmentCurrentPage + 1)" class="px-2 py-1 rounded bg-gray-100 text-gray-400 disabled:opacity-50">
                &gt;
              </button>
            </div>
          </div>
        </template>
      </div>

      <!-- Student Tab -->
      <div v-else>
        <h2 class="text-xl font-semibold mb-4">Student</h2>

        <!-- Filters -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
          <select
            v-model="selectedClass"
            class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm"
          >
            <option value="">Class</option>
            <option v-for="classItem in classList" :key="classItem" :value="classItem">
              {{ classItem }}
            </option>
          </select>
          <select
            v-model="selectedStatus"
            class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm"
          >
            <option value="">Status Class</option>
            <option value="Passed">Passed</option>
            <option value="On Going">On Going</option>
            <option value="Not passed">Not passed</option>
          </select>
        </div>

        <!-- Student Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full text-sm border border-gray-200 rounded-md overflow-hidden">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-2 text-left font-medium text-gray-600">Student</th>
                <th class="px-4 py-2 text-left font-medium text-gray-600">Class</th>
                <th class="px-4 py-2 text-left font-medium text-gray-600">Modul</th>
                <th class="px-4 py-2 text-left font-medium text-gray-600">Total Grades</th>
                <th class="px-4 py-2 text-left font-medium text-gray-600">Status</th>
                <th class="px-4 py-2 text-left font-medium text-gray-600">Progress</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(student, idx) in paginatedStudents"
                :key="idx"
                class="border-t cursor-pointer hover:bg-gray-50"
                @click="openStudentDetail(student)"
              >
                <td class="px-4 py-3 flex items-center gap-2">
                  <img :src="student.image" class="w-8 h-8 rounded-full object-cover" />
                  {{ student.name }}
                </td>
                <td class="px-4 py-3">{{ student.class }}</td>
                <td class="px-4 py-3">{{ student.modul }}</td>
                <td class="px-4 py-3">{{ student.grade || '-' }}</td>
                <td class="px-4 py-3">
                  <span
                    :class="{
                      'text-green-500 bg-green-100 px-2 py-1 rounded-full text-xs': student.status === 'Passed',
                      'text-blue-500 bg-blue-100 px-2 py-1 rounded-full text-xs': student.status === 'On Going',
                      'text-red-500 bg-red-100 px-2 py-1 rounded-full text-xs': student.status === 'Not passed'
                    }"
                  >
                    {{ student.status }}
                  </span>
                </td>
                <td class="px-4 py-3">{{ student.progress }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- Pagination Controls -->
        <div class="flex flex-col md:flex-row items-center justify-between mt-6 gap-4">
          <div class="flex items-center gap-2">
            <span>Show</span>
            <select v-model.number="rowsPerPage" class="border rounded px-2 py-1">
              <option v-for="opt in [10, 20, 50]" :key="opt" :value="opt">{{ opt }}</option>
            </select>
            <span>Row</span>
          </div>
          <div class="flex items-center gap-1">
            <button :disabled="currentPage === 1" @click="goToPage(currentPage - 1)" class="px-2 py-1 rounded bg-gray-100 text-gray-400 disabled:opacity-50">
              &lt;
            </button>
            <template v-for="page in visiblePages">
              <button
                v-if="page !== '...'"
                :key="page"
                @click="goToPage(page)"
                :class="[currentPage === page ? 'bg-[#F2720C] text-white' : 'bg-white text-gray-800', 'px-3 py-1 rounded font-semibold transition']"
              >
                {{ page }}
              </button>
              <span v-else :key="'ellipsis-' + page" class="px-2">...</span>
            </template>
            <button :disabled="currentPage === totalPages" @click="goToPage(currentPage + 1)" class="px-2 py-1 rounded bg-gray-100 text-gray-400 disabled:opacity-50">
              &gt;
            </button>
          </div>
        </div>
      </div>
    </div>
    <StudentDetailModal
      v-if="showStudentDetail"
      :student="selectedStudent"
      :show="showStudentDetail"
      @close="showStudentDetail = false"
    />
    <SubmissionReviewModal
      v-if="showReviewModal"
      :student="reviewStudent"
      :show="showReviewModal"
      @close="showReviewModal = false"
      @submit="handleReviewSubmit"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import NavbarAcademy from '@/components/NavbarAcademy.vue'
import StudentDetailModal from '@/components/StudentDetailModal.vue'
import SubmissionReviewModal from '@/components/SubmissionReviewModal.vue'

const activeTab = ref('assignment')
const selectedClass = ref('')
const selectedStatus = ref('')
const showStudentDetail = ref(false)
const selectedStudent = ref(null)
const rowsPerPage = ref(10)
const currentPage = ref(1)
const showAssignmentDetail = ref(false)
const selectedAssignment = ref({})
const showSortDropdown = ref(false)
const sortBy = ref('az')
const showSortDropdownAssignment = ref(false)
const sortByAssignment = ref('az')
const showReviewModal = ref(false)
const reviewStudent = ref(null)
let sortDropdownRef = null

const classList = [
  'Mastery In Golang',
  'UI/UX Bootcamp',
  'AI for Everyone',
  'Backend Mastery',
]

const assignments = [
  { title: 'Task Pengenalan Golang & Setup' },
  { title: 'Task Struktur Data Golang' },
  { title: 'Task Penerapan OOP dalam Golang' },
]

const students = [
  {
    name: 'jasmine',
    class: 'Mastery In Golang: From Zero to Hero',
    modul: '10/10',
    grade: 100,
    status: 'Passed',
    progress: '100%',
    image: '/profilepic.png'
  },
  {
    name: 'Paityn Press',
    class: 'UI/UX Bootcamp: Design Thinking',
    modul: '14/14',
    grade: 80,
    status: 'Passed',
    progress: '100%',
    image: '/profilepic.png'
  },
  {
    name: 'Tatiana Passa',
    class: 'Mastery In Golang: From Zero to Hero',
    modul: '7/10',
    grade: null,
    status: 'On Going',
    progress: '75%',
    image: '/profilepic.png'
  },
  {
    name: 'Justin Botosh',
    class: 'Backend Mastery: Build REST API',
    modul: '12/16',
    grade: null,
    status: 'Not passed',
    progress: '75%',
    image: '/profilepic.png'
  },
  {
    name: 'Emery Franci',
    class: 'Backend Mastery: Build REST API',
    modul: '6/12',
    grade: null,
    status: 'Not passed',
    progress: '50%',
    image: '/profilepic.png'
  }
]

const filteredStudents = computed(() => {
  return students.filter(s => {
    const classMatch = !selectedClass.value || s.class.includes(selectedClass.value)
    const statusMatch = !selectedStatus.value || s.status === selectedStatus.value
    return classMatch && statusMatch
  })
})

const paginatedStudents = computed(() => {
  const start = (currentPage.value - 1) * rowsPerPage.value
  return filteredStudents.value.slice(start, start + rowsPerPage.value)
})

const totalPages = computed(() => {
  return Math.max(1, Math.ceil(filteredStudents.value.length / rowsPerPage.value))
})

function goToPage(page) {
  if (page < 1 || page > totalPages.value) return
  currentPage.value = page
}

watch([rowsPerPage, filteredStudents], () => {
  currentPage.value = 1
})

// Pagination page numbers logic (with ...)
const visiblePages = computed(() => {
  const pages = []
  if (totalPages.value <= 7) {
    for (let i = 1; i <= totalPages.value; i++) pages.push(i)
  } else {
    if (currentPage.value <= 4) {
      pages.push(1, 2, 3, 4, 5, '...', totalPages.value)
    } else if (currentPage.value >= totalPages.value - 3) {
      pages.push(1, '...', totalPages.value - 4, totalPages.value - 3, totalPages.value - 2, totalPages.value - 1, totalPages.value)
    } else {
      pages.push(1, '...', currentPage.value - 1, currentPage.value, currentPage.value + 1, '...', totalPages.value)
    }
  }
  return pages
})

function openStudentDetail(student) {
  selectedStudent.value = student
  showStudentDetail.value = true
}

const assignmentStudents = ref([
  { name: 'jasmine', image: '/profilepic.png', submissionDate: '1 February 2025', status: 'on time', gradingDate: '3 February 2025', grade: 100 },
  { name: 'Paityn Press', image: '/profilepic.png', submissionDate: '1 February 2025', status: 'ontime', gradingDate: '3 February 2025', grade: 80 },
  { name: 'Alfonso Kenter', image: '/profilepic.png', submissionDate: '2 February 2025', status: 'ontime', gradingDate: '3 February 2025', grade: 90 },
  { name: 'Tatiana Passaquindi...', image: '/profilepic.png', submissionDate: '3 February 2025', status: 'Overdue', gradingDate: '3 February 2025', grade: 70 },
  { name: 'Justin Botosh', image: '/profilepic.png', submissionDate: '5 February 2025', status: 'overdue', gradingDate: '', grade: null },
  { name: 'Emery Franci', image: '/profilepic.png', submissionDate: '5 February 2025', status: 'ontime', gradingDate: '', grade: null },
])
const assignmentRowsPerPage = ref(10)
const assignmentCurrentPage = ref(1)

const sortedAssignmentStudents = computed(() => {
  let arr = [...assignmentStudents.value]
  if (sortBy.value === 'az') {
    arr.sort((a, b) => a.name.localeCompare(b.name))
  } else if (sortBy.value === 'za') {
    arr.sort((a, b) => b.name.localeCompare(a.name))
  } else if (sortBy.value === 'overdue') {
    arr.sort((a, b) => {
      const aOver = (a.status || '').toLowerCase().includes('overdue') ? 0 : 1
      const bOver = (b.status || '').toLowerCase().includes('overdue') ? 0 : 1
      return aOver - bOver
    })
  } else if (sortBy.value === 'graded') {
    arr.sort((a, b) => (b.grade !== null ? 1 : 0) - (a.grade !== null ? 1 : 0))
  } else if (sortBy.value === 'notgraded') {
    arr.sort((a, b) => (a.grade === null ? 0 : 1) - (b.grade === null ? 0 : 1))
  }
  return arr
})
const paginatedAssignmentStudents = computed(() => {
  const start = (assignmentCurrentPage.value - 1) * assignmentRowsPerPage.value
  return sortedAssignmentStudents.value.slice(start, start + assignmentRowsPerPage.value).map(student => {
    let gradingDate = student.gradingDate
    if (!gradingDate) {
      const dateMatch = (student.submissionDate || '').match(/(\d{1,2}) (\w+) (\d{4})/)
      if (dateMatch) {
        const [_, day, month, year] = dateMatch
        const months = ['January','February','March','April','May','June','July','August','September','October','November','December']
        const monthIdx = months.findIndex(m => m === month)
        if (monthIdx !== -1) {
          const d = new Date(Number(year), monthIdx, Number(day) + 2)
          gradingDate = `${d.getDate()} ${months[d.getMonth()]} ${d.getFullYear()}`
        }
      }
      gradingDate = gradingDate || '1 January 2025'
    }
    return { ...student, gradingDate }
  })
})
const assignmentTotalPages = computed(() => {
  return Math.max(1, Math.ceil(sortedAssignmentStudents.value.length / assignmentRowsPerPage.value))
})
function goToAssignmentPage(page) {
  if (page < 1 || page > assignmentTotalPages.value) return
  assignmentCurrentPage.value = page
}
watch([assignmentRowsPerPage, assignmentStudents, sortBy], () => {
  assignmentCurrentPage.value = 1
})
const assignmentVisiblePages = computed(() => {
  const pages = []
  if (assignmentTotalPages.value <= 7) {
    for (let i = 1; i <= assignmentTotalPages.value; i++) pages.push(i)
  } else {
    if (assignmentCurrentPage.value <= 4) {
      pages.push(1, 2, 3, 4, 5, '...', assignmentTotalPages.value)
    } else if (assignmentCurrentPage.value >= assignmentTotalPages.value - 3) {
      pages.push(1, '...', assignmentTotalPages.value - 4, assignmentTotalPages.value - 3, assignmentTotalPages.value - 2, assignmentTotalPages.value - 1, assignmentTotalPages.value)
    } else {
      pages.push(1, '...', assignmentCurrentPage.value - 1, assignmentCurrentPage.value, assignmentCurrentPage.value + 1, '...', assignmentTotalPages.value)
    }
  }
  return pages
})
function openAssignmentDetail(item) {
  selectedAssignment.value = item
  showAssignmentDetail.value = true
}
function setSort(val) {
  sortBy.value = val
  showSortDropdown.value = false
}
function filterAssignmentByClass(className) {
  selectedClass.value = className
  showAssignmentDetail.value = false
}
const filteredAssignments = computed(() => {
  if (!selectedClass.value) return assignments
  return assignments.filter(a => a.class === selectedClass.value)
})

const sortedAssignments = computed(() => {
  let arr = [...filteredAssignments.value]
  if (sortByAssignment.value === 'az') {
    arr.sort((a, b) => a.title.localeCompare(b.title))
  } else if (sortByAssignment.value === 'za') {
    arr.sort((a, b) => b.title.localeCompare(a.title))
  }
  return arr
})
function setSortAssignment(val) {
  sortByAssignment.value = val
  showSortDropdownAssignment.value = false
}

function handleClickOutside(event) {
  if (sortDropdownRef && !sortDropdownRef.contains(event.target)) {
    showSortDropdown.value = false
  }
}
onMounted(() => {
  window.addEventListener('mousedown', handleClickOutside)
})
onBeforeUnmount(() => {
  window.removeEventListener('mousedown', handleClickOutside)
})

function openReviewModal(student) {
  reviewStudent.value = student
  showReviewModal.value = true
}
function handleReviewSubmit({ review, grades }) {
  if (reviewStudent.value) {
    const idx = assignmentStudents.value.findIndex(s => s.name === reviewStudent.value.name)
    if (idx !== -1) {
      assignmentStudents.value[idx].grade = grades
      assignmentStudents.value[idx].review = review
    }
  }
  showReviewModal.value = false
}
</script>
