<template>
  <div class="bg-white rounded-[20px] p-4 sm:p-5 md:p-7 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
    <!-- Card <PERSON>er -->
    <h3 class="text-lg sm:text-xl md:text-[22px] font-medium text-gray-800 mb-3 sm:mb-5">{{ classData.title }}</h3>

    <!-- Card <PERSON>ats -->
    <div class="flex flex-wrap items-center gap-3 sm:gap-4 md:gap-8 mb-3 sm:mb-5">
      <!-- Rating -->
      <div class="flex items-center gap-2">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
        </svg>
        <span class="text-gray-600 text-sm sm:text-base">{{ classData.rating }}</span>
      </div>

      <!-- Students -->
      <div class="flex items-center gap-2">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M4.5 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM14.25 8.625a3.375 3.375 0 116.75 0 3.375 3.375 0 01-6.75 0zM1.5 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM17.25 19.128l-.001.144a2.25 2.25 0 01-.233.96 10.088 10.088 0 01-5.092 1.518c-1.033 0-2.021-.196-2.945-.537" />
        </svg>
        <span class="text-gray-600 text-sm sm:text-base">{{ classData.studentsEnrolled }} Students</span>
      </div>

      <!-- Modules -->
      <div class="flex items-center gap-2">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M11.7 2.805a.75.75 0 01.6 0A60.65 60.65 0 0122.83 8.72a.75.75 0 01-.231 1.337 49.949 49.949 0 00-9.902 3.912l-.003.002-.34.18a.75.75 0 01-.707 0A50.009 50.009 0 007.5 12.174v-.224c0-.131.067-.248.172-.311a54.614 54.614 0 014.653-2.52.75.75 0 00-.65-1.352 56.129 56.129 0 00-4.78 2.589 1.858 1.858 0 00-.859 1.228 49.803 49.803 0 00-4.634-1.527.75.75 0 01-.231-1.337A60.653 60.653 0 0111.7 2.805z" />
          <path d="M13.06 15.473a48.45 48.45 0 017.666-3.282c.134 1.414.22 2.843.255 4.285a.75.75 0 01-.46.71 47.878 47.878 0 00-8.105 4.342.75.75 0 01-.832 0 47.877 47.877 0 00-8.104-4.342.75.75 0 01-.461-.71c.035-1.442.121-2.87.255-4.286A48.4 48.4 0 016 13.18v1.27a1.5 1.5 0 00-.14 2.508c-.09.38-.222.753-.397 1.11.452.213.901.434 1.346.661a6.729 6.729 0 00.551-1.608 1.5 1.5 0 00.14-2.67v-.645a48.549 48.549 0 013.44 1.668 2.25 2.25 0 002.12 0z" />
          <path d="M4.462 19.462c.42-.419.753-.89 1-1.394.453.213.902.434 1.347.661a6.743 6.743 0 01-1.286 1.794.75.75 0 11-1.06-1.06z" />
        </svg>
        <span class="text-gray-600 text-sm sm:text-base">{{ classData.modules }} Modules</span>
      </div>
    </div>

    <!-- Description -->
    <p class="text-gray-500 text-sm sm:text-base leading-relaxed mb-4 sm:mb-6 md:mb-8">{{ classData.description }}</p>

    <!-- See Class Button -->
    <div class="flex justify-end">
      <button
        :class="[
          'px-4 sm:px-6 py-2 sm:py-2.5 text-white rounded-lg transition-colors w-[100px] sm:w-[120px]',
          buttonText === 'View Class'
            ? 'bg-orange hover:bg-orange-dark'
            : 'relative bg-[#757575] overflow-hidden group hover:text-white'
        ]"
        @click="$emit('view-class', classData.id)"
      >
        <span :class="[
          'text-sm sm:text-[15px] font-medium',
          buttonText !== 'View Class' ? 'relative z-10' : ''
        ]">{{ buttonText }}</span>
        <div v-if="buttonText !== 'View Class'" class="absolute inset-0 bg-orange transform translate-y-full transition-transform duration-300 ease-out group-hover:translate-y-0"></div>
      </button>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  classData: {
    type: Object,
    required: true,
    validator: (prop) => {
      // Required properties
      const requiredProps = [
        'id',
        'title',
        'rating',
        'studentsEnrolled',
        'modules',
        'description'
      ];

      // Enhanced debugging for the prop
      console.log('ClassCard received prop:', prop);
      console.log('ClassCard prop type:', typeof prop);
      console.log('ClassCard prop keys:', Object.keys(prop));
      console.log('ClassCard required properties check:', {
        id: 'id' in prop,
        title: 'title' in prop,
        rating: 'rating' in prop,
        studentsEnrolled: 'studentsEnrolled' in prop,
        modules: 'modules' in prop,
        description: 'description' in prop
      });

      // Check if all required properties exist
      const hasRequiredProps = requiredProps.every(key => {
        const hasKey = key in prop;
        if (!hasKey) {
          console.warn(`ClassCard missing required property: ${key}`);
        }
        return hasKey;
      });

      // If status is not provided, we'll use 'available' as default
      if (!('status' in prop)) {
        console.log('Setting default status: available');
        prop.status = 'available';
      }

      // If materials is not provided, we'll use an empty array as default
      if (!('materials' in prop)) {
        console.log('Setting default materials: []');
        prop.materials = [];
      }

      return hasRequiredProps;
    }
  },
  buttonText: {
    type: String,
    default: 'See Class'
  }
});


</script>