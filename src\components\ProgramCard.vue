<template>
  <div class="card rounded-xl overflow-hidden bg-white shadow-lg h-full transition-all duration-300 ease-in-out hover:-translate-y-1 hover:shadow-lg">
    <!-- Image Section with Pagination Dots -->
    <div class="relative">
      <img 
        :src="currentMentor.image" 
        :alt="currentMentor.name"
        class="w-full h-[240px] object-cover"
      >
      
      <!-- Mentor Name Badge -->
      <div class="absolute top-2 left-2 bg-gray-800/20 backdrop-blur-sm text-white text-sm px-4 py-2 rounded-lg">
        {{ currentMentor.name }}
      </div>
      
      <!-- Pagination Dots -->
      <div v-if="hasMentors" class="absolute bottom-6 left-0 right-0 flex justify-center gap-6">
        <button 
          v-for="(mentor, index) in mentorsList" 
          :key="index"
          @click="currentMentorIndex = index"
          class="w-5 h-5 rounded-full transition-all duration-200"
          :class="currentMentorIndex === index ? 'bg-white scale-125' : 'bg-white/50'"
          aria-label="Select mentor"
        ></button>
      </div>
    </div>
    
    <!-- Content Section -->
    <div class="p-5">
      <!-- Title and Description -->
      <h3 class="text-2xl font-bold text-gray-900 mb-2 text-center">{{ program.title }}</h3>
      <p class="text-gray-600 text-sm mb-4">{{ program.description }}</p>
      
      <!-- Price and Duration -->
      <div class="flex items-center gap-4 mb-4">
        <div class="flex items-center">
          <div class="w-6 h-6 flex items-center justify-center bg-gray-100 rounded-full mr-2">
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <span class="text-gray-700 font-medium">Rp {{ currentMentor.price.toLocaleString() }}</span>
        </div>
        
        <div class="flex items-center">
          <div class="w-6 h-6 flex items-center justify-center bg-gray-100 rounded-full mr-2">
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <span class="text-gray-700">{{ currentMentor.duration }}</span>
        </div>
      </div>
      
      <!-- Learn More Button -->
      <div class="flex justify-start">
        <router-link 
          :to="`/learn-more/${program.id}`" 
          class="text-teal no-underline inline-flex items-center gap-1 text-sm font-medium group hover:text-teal-dark transition-colors"
        >
          Learn More 
          <svg class="w-4 h-4 transition-transform duration-200 ease-in-out group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
          </svg>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, ref, computed, watch } from 'vue'

const props = defineProps({
  program: {
    type: Object,
    required: true
  },
  initialMentorIndex: {
    type: Number,
    default: 0
  }
});

const currentMentorIndex = ref(props.initialMentorIndex);

const mentorsList = computed(() => {
  if (props.program.mentors && Array.isArray(props.program.mentors)) {
    return props.program.mentors;
  } else if (props.program.mentor) {
    return [{
      id: 1,
      name: props.program.mentor,
      image: props.program.mentorImage,
      price: props.program.price,
      duration: props.program.duration
    }];
  }
  return [];
});

const hasMentors = computed(() => mentorsList.value.length > 1);
watch(() => props.program.id, () => {
  currentMentorIndex.value = 0;
});

watch(currentMentorIndex, (newIndex) => {
  if (newIndex < 0 || newIndex >= mentorsList.value.length) {
    currentMentorIndex.value = 0;
  }
});

const currentMentor = computed(() => {
  return mentorsList.value[currentMentorIndex.value] || {
    name: props.program.mentor || 'Unknown',
    image: props.program.mentorImage || '/default.png',
    price: props.program.price || 0,
    duration: props.program.duration || 'N/A'
  };
});
</script>