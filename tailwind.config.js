/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    "./node_modules/flowbite/**/*.js"
  ],
  theme: {
    extend: {
      fontFamily: {
        'gabarito': ['Gabarito', 'sans-serif'],
      },
      colors: {
        'primary': '#ff8c00',
        'primary-hover': '#e67e00',
        'teal': '#006D77',
        'teal-dark': '#005a63',
        'orange': {
          DEFAULT: '#FF8C00',
          dark: '#FF7C00',
          '600': '#E67E00',
          '50': '#FFF8F0',
          '100': '#FFEBD9',
        },
        'success': '#10b981',
        'warning': '#f59e0b',
        'info': '#3b82f6',
      },
      animation: {
        'pulse-once': 'pulse 1s ease-in-out 1',
      }
    },
  },
  plugins: [
    require('flowbite/plugin'),
    require('@tailwindcss/forms')
  ],
}