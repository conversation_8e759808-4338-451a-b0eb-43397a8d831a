<template>
  <div class="h-screen flex flex-col lg:flex-row bg-white overflow-hidden">
    <!-- Left Section -->
    <div class="h-full w-full lg:w-1/2 p-3 lg:p-6 flex flex-col">
      <!-- Logo -->
      <div class="mb-2">
        <FlowCampLogo />
      </div>

      <div class="flex-1 flex flex-col justify-center max-w-[420px] mx-auto w-full">
        <!-- User Icon -->
        <div class="flex justify-center w-full mb-3">
          <div class="w-16 h-16 lg:w-20 lg:h-20 rounded-full flex items-center justify-center overflow-hidden shadow-md">
            <img
              src="/user.png"
              alt="User Icon"
              class="w-full h-full object-cover"
            />
          </div>
        </div>

        <!-- Login Form -->
        <div class="text-center mb-3">
          <h1 class="text-lg lg:text-xl font-medium text-gray-900">Sign In to your account</h1>
          <p class="text-xs lg:text-sm text-gray-500">Enter your details to sign in</p>
        </div>

        <!-- Google Sign In -->
        <button
          class="w-full flex items-center justify-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50/50 transition-colors disabled:bg-gray-100 disabled:cursor-not-allowed"
          @click="handleGoogleSignIn"
          :disabled="loading"
        >
          <svg v-if="loading && googleLoading" class="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <img v-else src="/google-icon.svg" alt="Google" class="w-4 h-4" />
          <span class="text-gray-700 text-sm">{{ loading && googleLoading ? 'Signing in...' : 'Google' }}</span>
        </button>

        <div class="relative flex items-center justify-center w-full my-2">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-200"></div>
          </div>
          <div class="relative bg-white px-4">
            <span class="text-xs text-gray-400">OR</span>
          </div>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-2">
          <!-- Username/Email Input -->
          <div class="space-y-1">
            <input
              v-model="form.email"
              type="email"
              placeholder="Username or email"
              class="w-full px-3 py-2 rounded-lg border text-sm"
              :class="[
                errors.email
                  ? 'border-red-500 focus:ring-red-200'
                  : 'border-gray-200 focus:border-[#FF6B00] focus:ring-orange-200'
              ]"
              @blur="validateEmail"
            />
            <p v-if="errors.email" class="text-red-500 text-xs">{{ errors.email }}</p>
          </div>

          <!-- Password Input -->
          <div class="space-y-1">
            <div class="relative">
              <input
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="Password"
                class="w-full px-3 py-2 rounded-lg border text-sm"
                :class="[
                  errors.password
                    ? 'border-red-500 focus:ring-red-200'
                    : 'border-gray-200 focus:border-[#FF6B00] focus:ring-orange-200'
                ]"
                @blur="validatePassword"
              />
              <button
                type="button"
                @click="togglePassword"
                class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-orange-200 rounded-full p-1"
                :aria-label="showPassword ? 'Hide password' : 'Show password'"
              >
                <!-- Eye icon when password is hidden -->
                <svg v-if="showPassword" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                <!-- Eye-off icon when password is visible -->
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                  <line x1="1" y1="1" x2="23" y2="23"></line>
                </svg>
              </button>
            </div>
            <p v-if="errors.password" class="text-red-500 text-xs">{{ errors.password }}</p>
          </div>

          <!-- Remember Me & Forgot Password -->
          <div class="flex items-center justify-between text-xs">
            <label class="flex items-center">
              <input
                v-model="form.remember"
                type="checkbox"
                class="w-3 h-3 accent-[#FF6B00] border-gray-200 rounded"
              />
              <span class="ml-2 text-gray-600">Keep me logged in</span>
            </label>
            <router-link to="/email-code" class="text-[#FF6B00] hover:text-[#FF5500]">Forgot password?</router-link>
          </div>

          <!-- Sign In Button -->
          <button
            type="submit"
            class="w-full bg-[#FF6B00] text-white py-2 rounded-lg text-sm font-medium hover:bg-[#FF5500] disabled:bg-orange-300 disabled:cursor-not-allowed flex items-center justify-center"
            :disabled="loading || !isFormValid"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ loading ? 'Signing In...' : 'Sign In' }}
          </button>
        </form>

        <!-- Sign Up Link -->
        <p class="text-center text-xs text-gray-600 mt-2">
          Don't have an account?
          <router-link to="/register" class="text-[#FF6B00] hover:text-[#FF5500] font-medium">Sign Up</router-link>
        </p>
      </div>

      <!-- Footer -->
      <div class="mt-2 px-3">
        <div class="flex items-center justify-between text-xs text-gray-500">
          <span>© 2025 FlowCamp.id</span>
        </div>
      </div>
    </div>

    <!-- Right Section -->
    <div class="hidden lg:block h-full lg:w-1/2 bg-[#FF6B00] relative overflow-hidden">
      <!-- Decorative Circles -->
      <div class="absolute w-32 h-32 bg-white/10 rounded-full -top-16 -right-16"></div>
      <div class="absolute w-24 h-24 bg-white/10 rounded-full top-1/4 left-12"></div>
      <div class="absolute w-16 h-16 bg-white/10 rounded-full bottom-32 right-24"></div>

      <!-- Right Section Content -->
      <div class="h-full flex flex-col justify-between p-6">
        <!-- Cards Container -->
        <div class="flex-1 flex items-center justify-center">
            <div class="relative w-full max-w-2xl mx-auto" v-for="(slide, index) in slides" :key="index" v-show="currentSlide === index">
            <!-- Progress Card -->
            <div class="absolute transform -translate-x-1/2 left-1/2 -translate-y-24 z-0">
              <img
              :src="slide.progressWidget"
              alt="Progress Widget"
                class="w-[500px] lg:w-[700px] object-contain"
              />
            </div>

            <!-- Course Card -->
            <div class="absolute transform -translate-x-1/4 left-1/2 translate-y-16 z-10">
              <img
              :src="slide.courseCard"
              alt="Course Card"
                class="w-[500px] lg:w-[580px] object-contain"
              />
            </div>
            </div>
        </div>

        <!-- Title & Description -->
        <div class="text-center text-white mt-36 lg:mt-48">
          <h2 class="text-lg lg:text-2xl font-bold tracking-tight mb-2 px-4">
            {{ slides[currentSlide].title }}
          </h2>
          <p class="text-xs lg:text-sm text-white/90 max-w-[480px] mx-auto px-4">
            {{ slides[currentSlide].description }}
          </p>
        </div>

        <!-- Pagination Dots -->
        <div class="flex justify-center items-center gap-1.5 mt-6">
          <button
            v-for="(_, index) in slides"
            :key="index"
            @click="currentSlide = index"
            class="w-2 h-2 rounded-full transition-all duration-300"
            :class="currentSlide === index ? 'bg-white scale-125' : 'bg-white/50 hover:bg-white/70'"
            :aria-label="`Go to slide ${index + 1}`"
          ></button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import FlowCampLogo from '@/components/FlowCampLogo.vue';

const router = useRouter();
const loading = ref(false);
const googleLoading = ref(false);
const showPassword = ref(false);

const form = reactive({
  email: '',
  password: '',
  remember: false
});

const errors = reactive({
  email: '',
  password: ''
});

// Email validation using regex
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};



// Validate email field
const validateEmail = () => {
  errors.email = '';

  if (!form.email) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(form.email)) {
    errors.email = 'Please enter a valid email address';
  }
};

// Validate password field
const validatePassword = () => {
  errors.password = '';

  if (!form.password) {
    errors.password = 'Password is required';
  }
};

// Check if form is valid
const isFormValid = computed(() => {
  return form.email &&
         form.password &&
         isValidEmail(form.email) &&
         !errors.email &&
         !errors.password;
});

const currentSlide = ref(0);

const slides = [
  {
    progressWidget: '/widget.png',
    courseCard: '/card.png',
    title: 'Kick-Start Your Career With Our Industry Leading Courses',
    description: 'Start your new career today with our selection of accredited editing courses. Learn from our world-class course content, engage with our expert tutor team, and enter the industry immediately with our work guarantee.'
  },
  {
    progressWidget: '/widget-2.png',
    courseCard: '/card-2.png',
    title: 'Learn From Industry Experts',
    description: 'Get mentored by professionals with years of experience in the field. Our instructors bring real-world knowledge and practical insights to help you succeed.'
  },
  {
    progressWidget: '/widget-3.png',
    courseCard: '/card-3.png',
    title: 'Flexible Learning Path',
    description: 'Study at your own pace with our flexible learning options. Access course materials 24/7 and learn when it suits you best.'
  }
];

const handleGoogleSignIn = async () => {
  try {
    loading.value = true;
    googleLoading.value = true;

    // Simulate Google OAuth API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    // If successful, redirect to Home page
    router.push('/');
  } catch (error) {
    console.error('Google sign in error:', error);
    // Show an alert for Google sign-in errors
    alert('Failed to sign in with Google. Please try again.');
  } finally {
    loading.value = false;
    googleLoading.value = false;
  }
};

const handleSubmit = async () => {
  // Reset errors
  errors.email = '';
  errors.password = '';

  // Validate all fields
  validateEmail();
  validatePassword();

  // If there are any errors, stop submission
  if (errors.email || errors.password) {
    return;
  }

  try {
    loading.value = true;

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    // If successful, redirect to Home page
    router.push('/');
  } catch (error) {
    console.error('Login error:', error);
    // Show a generic error message if login fails
    errors.email = 'Invalid email or password';
  } finally {
    loading.value = false;
  }
};

const togglePassword = () => {
  showPassword.value = !showPassword.value;
};
</script>
