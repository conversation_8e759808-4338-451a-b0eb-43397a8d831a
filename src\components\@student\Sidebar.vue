<template>
  <div>
    <!-- Backdrop overlay when sidebar is open (mobile only) -->
    <div
      v-if="isOpen"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
      @click="toggleSidebar"
    ></div>

    <!-- Sidebar -->
    <div
      class="fixed top-0 left-0 h-full bg-white shadow-lg z-50 transition-all duration-500 ease-in-out transform"
      :class="[isOpen ? 'w-64 translate-x-0' : 'w-0 -translate-x-full overflow-hidden']"
      @click.stop
    >
      <!-- Sidebar content wrapper with animation -->
      <div class="flex flex-col h-full transition-opacity duration-300 ease-in-out"
           :class="{ 'opacity-0': !isOpen, 'opacity-100 delay-100': isOpen }">
        <!-- Header with logo and close button -->
        <div class="flex items-center justify-between p-4 border-b">
          <div class="flex items-center transition-all duration-500 ease-in-out transform">
            <img src="/logo.png" alt="Flow Camp" class="h-8 transition-transform duration-500 ease-in-out" />
            <span class="ml-2 text-orange-500 font-bold transition-all duration-500 ease-in-out">Flow Camp</span>
          </div>
          <button
            @click="toggleSidebar"
            class="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Search input -->
        <div class="p-4 border-b">
          <div class="relative">
            <input
              type="text"
              placeholder="Search"
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

      <!-- Navigation sections -->
      <div class="flex-grow overflow-y-auto">
        <!-- MAIN section -->
        <div class="p-4">
          <div class="text-xs font-semibold text-gray-500 mb-2">MAIN</div>
          <ul class="space-y-1">
            <li>
              <router-link
                to="/student/dashboard"
                class="flex items-center px-3 py-2 rounded-md transition-colors"
                :class="isActive('/student/dashboard') ? 'bg-orange-100 text-orange-500' : 'text-gray-700 hover:bg-gray-100'"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span>Dashboard</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/exams"
                class="flex items-center px-3 py-2 rounded-md transition-colors text-gray-700 hover:bg-gray-100"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <span>Exams</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/questions"
                class="flex items-center px-3 py-2 rounded-md transition-colors text-gray-700 hover:bg-gray-100"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Questions</span>
              </router-link>
            </li>
          </ul>
        </div>

        <!-- MANAGEMENT section -->
        <div class="p-4">
          <div class="text-xs font-semibold text-gray-500 mb-2">MANAGEMENT</div>
          <ul class="space-y-1">
            <li>
              <router-link
                to="/statistics"
                class="flex items-center px-3 py-2 rounded-md transition-colors text-gray-700 hover:bg-gray-100"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span>Statistics</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/certificates"
                class="flex items-center px-3 py-2 rounded-md transition-colors text-gray-700 hover:bg-gray-100"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <span>Certificates</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/surveys"
                class="flex items-center px-3 py-2 rounded-md transition-colors text-gray-700 hover:bg-gray-100"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <span>Surveys</span>
              </router-link>
            </li>
          </ul>
        </div>

        <!-- SYSTEM section -->
        <div class="p-4">
          <div class="text-xs font-semibold text-gray-500 mb-2">SYSTEM</div>
          <ul class="space-y-1">
            <li>
              <router-link
                to="/settings"
                class="flex items-center px-3 py-2 rounded-md transition-colors text-gray-700 hover:bg-gray-100"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>Settings</span>
              </router-link>
            </li>
            <li>
              <router-link
                to="/help"
                class="flex items-center px-3 py-2 rounded-md transition-colors text-gray-700 hover:bg-gray-100"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Help</span>
              </router-link>
            </li>
          </ul>
        </div>
      </div>

      <!-- User profile -->
      <div class="p-4 border-t flex items-center">
        <img src="/prof.png" alt="User Profile" class="h-8 w-8 rounded-full" />
        <span class="ml-2 text-gray-700">adadada</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['toggle']);

// Route
const route = useRoute();

// Methods
const toggleSidebar = () => {
  emit('toggle');
};

// Computed
const isActive = (path) => {
  return route.path === path;
};
</script>
