<template>
  <nav class="sticky top-0 z-50 bg-white shadow-sm">
    <!-- Main navbar wrapper with exact same responsive adjustments as content -->
    <div
      class="w-full transition-all duration-300 ease-in-out relative bg-white"
      :class="[
        {
          'md:ml-64': isSidebarOpen,
          'ml-0': !isSidebarOpen
        }
      ]"
    >
      <!-- Border that adjusts with content width -->
      <div class="absolute bottom-0 left-0 right-0 h-[1px] bg-gray-200 transition-all duration-300 ease-in-out"></div>

      <!-- Full-width background to ensure consistency -->
      <div class="absolute top-0 right-0 w-screen h-full bg-white -z-10"></div>

      <!-- Sidebar Toggle Button - Visible only when sidebar is closed -->
      <div
        class="absolute left-0 top-0 flex items-center h-16 z-20 transition-all duration-300 ease-in-out"
        :class="{'opacity-0 invisible md:pointer-events-none': isSidebarOpen, 'opacity-100 visible pointer-events-auto': !isSidebarOpen}"
      >
        <div class="pl-2 pr-1 flex items-center pointer-events-auto">
          <SidebarToggle @toggle="toggleSidebar" />
        </div>
      </div>

      <!-- Logo - Visible only when sidebar is closed -->
      <div
        class="absolute top-0 left-10 flex items-center h-16 z-20 transition-all duration-300 ease-in-out"
        :class="{'opacity-0 invisible md:pointer-events-none': isSidebarOpen, 'opacity-100 visible pointer-events-auto': !isSidebarOpen}"
      >
        <div class="flex items-center transition-all duration-300 ease-in-out">
          <router-link to="/" class="flex items-center">
            <img src="/logo.png" alt="FlowCamp Logo" class="h-8 ml-2" />
            <span class="ml-2 text-orange font-bold">Flow Camp</span>
          </router-link>
        </div>
      </div>

      <!-- Main navbar content with exact same responsive adjustments as content -->
      <div
        class="w-full flex flex-wrap items-center justify-between h-16 transition-all duration-300 ease-in-out relative z-10"
        :class="[
          {
            'px-4 sm:px-6 md:px-8': true,
            'pl-40 sm:pl-48 md:pl-56': !isSidebarOpen,
            'pl-64 md:pl-72': isSidebarOpen
          }
        ]">

      <!-- Navigation Links - Centered -->
      <div class="hidden md:flex md:flex-1 md:justify-center order-1 transition-all duration-300 ease-in-out">
        <ul class="flex flex-row space-x-8 font-medium">
          <li class="relative">
            <router-link
              to="/student/dashboard"
              class="block py-2 px-3 rounded transition-all duration-200 hover:text-orange"
              :class="isHomeActive ? 'text-orange font-semibold' : 'text-gray-700'"
            >
              Home
              <div v-if="isHomeActive" class="absolute h-0.5 bg-orange w-full bottom-[-5px] left-0 transition-all duration-300"></div>
            </router-link>
          </li>
          <li class="relative">
            <a
              href="#"
              @click="navigateToAcademy"
              class="block py-2 px-3 rounded transition-all duration-200 hover:text-orange"
              :class="isAcademyActive ? 'text-orange font-semibold' : 'text-gray-700'"
            >
              Academy
              <div v-if="isAcademyActive" class="absolute h-0.5 bg-orange w-full bottom-[-5px] left-0 transition-all duration-300"></div>
            </a>
          </li>
          <li class="relative">
            <router-link
              to="/student/classes"
              custom
              v-slot="{ navigate }"
            >
              <a
                href="#"
                @click="navigateToClasses"
                class="block py-2 px-3 rounded transition-all duration-200 hover:text-orange"
                :class="isClassesActive ? 'text-orange font-semibold' : 'text-gray-700'"
              >
                Classes
                <div v-if="isClassesActive" class="absolute h-0.5 bg-orange w-full bottom-[-5px] left-0 transition-all duration-300"></div>
              </a>
            </router-link>
          </li>
        </ul>
      </div>

      <!-- Right Side - Notifications and Profile (always visible) -->
      <div class="flex items-center space-x-3 md:space-x-4 order-2">
        <!-- Notification Bell with improved hover effect -->
        <div class="relative">
          <button
            @click.stop="toggleNotifications"
            class="notification-bell relative p-1.5 rounded-full transition-all duration-200 group"
            :class="[
              showNotifications ? 'bg-orange-100 text-orange' : 'text-gray-700 hover:text-orange hover:bg-orange-50',
              unreadNotificationsCount > 0 ? 'animate-pulse-once' : ''
            ]"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 sm:h-6 sm:w-6 transition-transform duration-200 group-hover:scale-110"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <!-- Notification badge - only show unread count with animation -->
            <transition
              enter-active-class="transition-all duration-300 ease-in-out"
              enter-from-class="opacity-0 scale-50"
              enter-to-class="opacity-100 scale-100"
              leave-active-class="transition-all duration-200 ease-in-out"
              leave-from-class="opacity-100 scale-100"
              leave-to-class="opacity-0 scale-50"
            >
              <span
                v-if="unreadNotificationsCount > 0"
                class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-sm transform origin-center"
              >
                {{ unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount }}
              </span>
            </transition>
          </button>

          <!-- Notification Dropdown with transition -->
          <transition
            enter-active-class="transition ease-out duration-200"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-150"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-show="showNotifications"
              class="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden"
              ref="notificationDropdown"
              @click.stop
            >
              <!-- Header with Mark all as read option and notification count -->
              <div class="p-3 flex justify-between items-center bg-gray-50 border-b border-gray-200">
                <div class="flex items-center">
                  <h3 class="text-base font-semibold text-gray-800">Notifications</h3>
                  <!-- Notification count badge -->
                  <div
                    v-if="notifications.length > 0"
                    class="ml-2 px-1.5 py-0.5 bg-gray-200 text-gray-700 text-xs rounded-full"
                  >
                    {{ notifications.length }}
                  </div>
                  <!-- Unread count badge -->
                  <div
                    v-if="unreadNotificationsCount > 0"
                    class="ml-1 px-1.5 py-0.5 bg-orange text-white text-xs rounded-full flex items-center"
                  >
                    <span class="w-1 h-1 bg-white rounded-full mr-1"></span>
                    {{ unreadNotificationsCount }} new
                  </div>
                </div>

                <button
                  v-if="unreadNotificationsCount > 0"
                  @click.stop="markAllAsRead"
                  class="text-xs text-orange hover:text-orange-600 font-medium transition-colors flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Mark all as read
                </button>
              </div>

              <!-- Notification Items -->
              <div v-if="notifications.length > 0" class="max-h-[350px] overflow-y-auto">
                <div
                  v-for="(notification, index) in notifications"
                  :key="index"
                  class="group border-b border-gray-100 last:border-b-0 transition-all duration-200 relative"
                  :class="[
                    notification.read ? 'bg-gray-50/50 hover:bg-gray-50' : 'bg-white hover:bg-orange-50/30',
                  ]"
                >
                  <!-- Clickable area with proper padding -->
                  <div
                    @click="navigateToNotification(notification)"
                    class="p-3 cursor-pointer relative overflow-hidden notification-item-hover"
                  >
                    <!-- Unread indicator bar -->
                    <div
                      class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-10 rounded-r-full transition-all duration-200"
                      :class="notification.read ? 'bg-transparent' : 'bg-orange'"
                    ></div>

                    <!-- Main content with proper spacing -->
                    <div class="flex items-stretch pl-3 pr-1 min-h-[90px]">
                      <!-- Left content column -->
                      <div class="flex-1 w-full">
                        <!-- Header with title and NEW badge -->
                        <div class="flex items-center">
                          <p
                            class="text-sm transition-all duration-200 max-w-[180px]"
                            :class="notification.read ? 'text-gray-600' : 'text-gray-800 font-semibold'"
                          >
                            {{ notification.title }}
                          </p>

                          <!-- New indicator for unread notifications -->
                          <span
                            v-if="!notification.read"
                            class="ml-2 text-[10px] px-1 py-0.5 bg-orange text-white rounded-sm font-medium flex-shrink-0"
                          >
                            NEW
                          </span>
                        </div>

                        <!-- Class name for context -->
                        <p class="text-xs text-gray-500 mt-1 break-words pr-2">
                          {{ notification.className }}
                        </p>

                        <!-- Due date and status badges with improved layout -->
                        <div class="flex flex-wrap items-center mt-2 gap-2">
                          <!-- Due date badge -->
                          <span
                            v-if="notification.dueDate"
                            class="inline-flex items-center text-xs px-2 py-1 rounded-md shadow-sm"
                            :class="[
                              isDueSoon(notification)
                                ? 'text-red-600 bg-red-50 border border-red-100 font-medium'
                                : 'text-gray-600 bg-gray-100'
                            ]"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="break-words">{{ formatDueDate(notification) }}</span>
                          </span>

                          <!-- Task status badge with icon -->
                          <span
                            v-if="notification.taskStatus !== 'pending'"
                            class="inline-flex items-center text-xs px-2 py-1 rounded-md shadow-sm font-medium"
                            :class="getStatusColorClass(notification.taskStatus)"
                          >
                            <svg v-if="notification.taskStatus === 'submitted'" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <svg v-else-if="notification.taskStatus === 'under_review'" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <svg v-else-if="notification.taskStatus === 'reviewed' || notification.taskStatus === 'turned_in'" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <svg v-else-if="notification.taskStatus === 'past_due'" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="break-words">{{ formatTaskStatusShort(notification.taskStatus) }}</span>
                          </span>
                        </div>


                      </div>

                      <!-- Right column with timestamp and arrow -->
                      <div class="flex-shrink-0 flex flex-col items-end ml-2 h-full">
                        <!-- Timestamp in top right -->
                        <div class="flex items-center mt-1">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p class="text-xs text-gray-400">{{ notification.time }}</p>
                        </div>

                        <!-- Spacer to push arrow down -->
                        <div class="flex-grow"></div>

                        <!-- Right arrow indicator with better animation -->
                        <div class="transform translate-x-1 group-hover:translate-x-0 opacity-0 group-hover:opacity-100 transition-all duration-100 ease-in-out mt-6 ml-6 self-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Footer with Clear all button and improved styling -->
                <div class="p-3 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                  <span class="text-xs text-gray-500">
                    {{ notifications.length }} notification{{ notifications.length !== 1 ? 's' : '' }}
                  </span>
                  <button
                    @click.stop="clearAllNotifications"
                    class="text-xs text-gray-600 hover:text-gray-800 font-medium transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Clear all
                  </button>
                </div>
              </div>

              <!-- Empty State with improved styling and animation -->
              <div v-else class="py-12 px-4 text-center">
                <div class="w-20 h-20 mx-auto mb-5 rounded-full bg-gray-50 flex items-center justify-center relative overflow-hidden">
                  <!-- Bell icon with subtle animation -->
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-300 transform transition-transform duration-1000 hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                  </svg>

                  <!-- Decorative circles -->
                  <div class="absolute w-full h-full">
                    <div class="absolute top-1/4 left-1/4 w-1 h-1 bg-gray-200 rounded-full"></div>
                    <div class="absolute top-3/4 right-1/4 w-1.5 h-1.5 bg-gray-200 rounded-full"></div>
                    <div class="absolute bottom-1/4 right-1/3 w-1 h-1 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
                <h4 class="text-gray-700 font-medium mb-2">All caught up!</h4>
                <p class="text-gray-500 mb-1">There are no new notifications for you yet.</p>
                <p class="text-xs text-gray-400 max-w-[200px] mx-auto">
                  We'll notify you when there are new assignments or updates.
                </p>
              </div>
            </div>
          </transition>
        </div>

        <!-- Profile Dropdown -->
        <div class="relative">
          <button class="flex items-center space-x-1 focus:outline-none p-1 rounded-full hover:bg-orange-50 transition-colors">
            <img src="/prof.png" alt="User Profile" class="h-7 w-7 sm:h-8 sm:w-8 rounded-full object-cover border border-gray-300" />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 hidden sm:block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <!-- Dropdown menu would go here -->
        </div>

        <!-- Mobile menu button -->
        <button
          @click="toggleMobileMenu"
          type="button"
          class="inline-flex items-center p-1.5 ml-1 text-gray-500 rounded-lg md:hidden hover:bg-orange-50 focus:outline-none transition-colors"
          aria-controls="navbar-menu"
          :aria-expanded="mobileMenuOpen"
        >
          <span class="sr-only">Open main menu</span>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>

      <!-- Mobile Navigation Links -->
      <div
        class="w-full md:hidden order-3 transition-all duration-300 ease-in-out mt-4"
        :class="[mobileMenuOpen ? 'block' : 'hidden']"
      >
        <ul class="flex flex-col p-4 font-medium border border-gray-100 rounded-lg bg-gray-50">
          <li class="relative">
            <router-link
              to="/student/dashboard"
              class="block py-2 pl-3 pr-4 rounded transition-all duration-200 hover:bg-orange-100 md:hover:bg-transparent md:hover:text-orange"
              :class="isHomeActive ? 'text-orange font-semibold' : 'text-gray-700'"
              @click="mobileMenuOpen = false"
            >
              Home
            </router-link>
          </li>
          <li class="relative">
            <a
              href="#"
              @click="navigateToAcademy"
              class="block py-2 pl-3 pr-4 rounded transition-all duration-200 hover:bg-orange-100 md:hover:bg-transparent md:hover:text-orange"
              :class="isAcademyActive ? 'text-orange font-semibold' : 'text-gray-700'"
            >
              Academy
            </a>
          </li>
          <li class="relative">
            <router-link
              to="/student/classes"
              custom
              v-slot="{}"
            >
              <a
                href="#"
                @click="navigateToClasses"
                class="block py-2 pl-3 pr-4 rounded transition-all duration-200 hover:bg-orange-100 md:hover:bg-transparent md:hover:text-orange"
                :class="isClassesActive ? 'text-orange font-semibold' : 'text-gray-700'"
              >
                Classes
              </a>
            </router-link>
          </li>
        </ul>
      </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { STORAGE_KEYS, useClassStore } from '@/data/availableClasses';
import SidebarToggle from './SidebarToggle.vue';
import { toggleSidebar, isSidebarOpen } from '@/data/sidebarState';
import { formatDate, formatTime } from '@/utils/studentUtils';

const route = useRoute();
const router = useRouter();
const classStore = useClassStore();
const mobileMenuOpen = ref(false);
const showNotifications = ref(false);
const notificationDropdown = ref(null);

// Storage key for notifications
const NOTIFICATION_STORAGE_KEY = 'flowcamp-notifications';

// Notification state
const notifications = ref([]);

// Load notifications from localStorage or generate from class data
const loadNotifications = () => {
  const storedNotifications = localStorage.getItem(NOTIFICATION_STORAGE_KEY);

  if (storedNotifications) {
    notifications.value = JSON.parse(storedNotifications);
  } else {
    // Generate notifications from class materials with tasks
    generateNotificationsFromClasses();
  }
};

// Generate notifications from class materials
const generateNotificationsFromClasses = () => {
  const classes = classStore.classes.value;
  const newNotifications = [];

  // Get the current date for comparison
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);

  // Format dates for display
  const todayStr = 'Just now';
  const yesterdayStr = 'Yesterday';

  classes.forEach(classItem => {
    // Only include materials with tasks
    classItem.materials.forEach(material => {
      if (material.hasTask) {
        // Create a notification for each task
        const notificationDate = new Date(material.postedDate);
        let timeLabel = todayStr;

        // If posted yesterday, show "Yesterday"
        if (notificationDate < now && notificationDate >= yesterday) {
          timeLabel = yesterdayStr;
        } else if (notificationDate < yesterday) {
          // Format date for older notifications
          const options = { month: 'short', day: 'numeric' };
          timeLabel = notificationDate.toLocaleDateString(undefined, options);
        }

        // Create notification with more detailed information
        newNotifications.push({
          id: material.id,
          classId: classItem.id,
          materialTitle: material.title,
          className: classItem.title,
          title: `Assignment: ${material.title}`,
          time: timeLabel,
          read: false,
          date: material.postedDate,
          dueDate: material.dueDate,
          dueTime: material.dueTime,
          timestamp: notificationDate.getTime(),
          taskStatus: material.taskStatus || 'pending',
          // Add more details for better context
          taskDescription: material.taskDescription || '',
          isComplete: material.isComplete || false
        });
      }
    });
  });

  // Get stored notifications to preserve read status
  const storedNotifications = localStorage.getItem(NOTIFICATION_STORAGE_KEY);
  let existingNotifications = [];

  if (storedNotifications) {
    existingNotifications = JSON.parse(storedNotifications);

    // Update read status for existing notifications
    newNotifications.forEach(newNotif => {
      const existingNotif = existingNotifications.find(
        existing => existing.id === newNotif.id && existing.classId === newNotif.classId
      );

      if (existingNotif && existingNotif.read) {
        newNotif.read = true;
      }
    });
  }

  // Sort notifications: unread first, then by timestamp (newest first)
  newNotifications.sort((a, b) => {
    // First sort by read status (unread first)
    if (a.read !== b.read) {
      return a.read ? 1 : -1; // false comes before true
    }
    // Then sort by timestamp (newest first)
    return b.timestamp - a.timestamp;
  });

  // Take only the most recent notifications (limit to 10 for more context)
  notifications.value = newNotifications.slice(0, 10);

  // Save to localStorage
  saveNotifications();
};

// Save notifications to localStorage
const saveNotifications = () => {
  localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(notifications.value));
};

// Toggle notifications dropdown
const toggleNotifications = (event) => {
  // Prevent event propagation to avoid triggering handleClickOutside
  if (event) {
    event.stopPropagation();
  }

  // Toggle dropdown visibility
  showNotifications.value = !showNotifications.value;

  // Log for debugging
  console.log('Notification dropdown toggled:', showNotifications.value);
};

// Handle click outside to close notifications dropdown
const handleClickOutside = (event) => {
  // Check if click is outside notification dropdown and notification bell
  if (showNotifications.value &&
      notificationDropdown.value &&
      !notificationDropdown.value.contains(event.target) &&
      !event.target.closest('.notification-bell') &&
      !event.target.closest('button[class*="notification"]')) {
    showNotifications.value = false;
  }
};

// Helper functions for notification display
const formatDueDate = (notification) => {
  if (!notification.dueDate) return '';

  const dueDate = new Date(`${notification.dueDate}T${notification.dueTime || '23:59'}`);
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // If due today
  if (dueDate.toDateString() === now.toDateString()) {
    return `Today, ${formatTime(notification.dueTime)}`;
  }

  // If due tomorrow
  if (dueDate.toDateString() === tomorrow.toDateString()) {
    return `Tomorrow, ${formatTime(notification.dueTime)}`;
  }

  // Otherwise, show full date
  return `${formatDate(notification.dueDate)}, ${formatTime(notification.dueTime)}`;
};

// Check if a task is due soon (within 2 days)
const isDueSoon = (notification) => {
  if (!notification.dueDate) return false;

  const dueDate = new Date(`${notification.dueDate}T${notification.dueTime || '23:59'}`);
  const now = new Date();

  // Calculate difference in days
  const diffTime = dueDate.getTime() - now.getTime();
  const diffDays = diffTime / (1000 * 3600 * 24);

  // Return true if due within 2 days or past due
  return diffDays < 2;
};

// Get status color class for badges
const getStatusColorClass = (status) => {
  switch (status) {
    case 'submitted':
      return 'bg-blue-50 text-blue-600 border border-blue-100 shadow-sm';
    case 'under_review':
      return 'bg-orange-50 text-orange-600 border border-orange-100 shadow-sm';
    case 'reviewed':
    case 'turned_in':
      return 'bg-green-50 text-green-600 border border-green-100 shadow-sm';
    case 'past_due':
      return 'bg-red-50 text-red-600 border border-red-100 shadow-sm';
    default:
      return 'bg-gray-50 text-gray-600 border border-gray-100 shadow-sm';
  }
};

// Format task status for shorter display
const formatTaskStatusShort = (status) => {
  switch (status) {
    case 'submitted':
      return 'Submitted';
    case 'under_review':
      return 'Reviewing';
    case 'reviewed':
      return 'Reviewed';
    case 'turned_in':
      return 'Turned in';
    case 'past_due':
      return 'Past due';
    default:
      return 'Pending';
  }
};

// Computed property for unread notifications count
const unreadNotificationsCount = computed(() => {
  return notifications.value.filter(notification => !notification.read).length;
});

// Computed properties to check active pages
const isHomeActive = computed(() => {
  return route.path === '/student/dashboard';
});

const isAcademyActive = computed(() => {
  // Only highlight Academy when on academy or class detail pages, but not on classes page
  return route.path === '/student/academy' ||
         (route.path.startsWith('/student/class/') && route.path !== '/student/classes');
});

const isClassesActive = computed(() => {
  // Highlight Classes when on the classes page or class detail page
  return route.path === '/student/classes' || route.path.includes('/student/class-detail/');
});

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

// Helper function to clear navigation state
const clearNavigationState = () => {
  localStorage.removeItem(STORAGE_KEYS.CLASS_SOURCE);
  localStorage.removeItem(STORAGE_KEYS.CURRENT_CLASS_ID);
  console.log('Cleared navigation state from localStorage');
};

// Navigation handlers with force reload
const navigateToAcademy = (event) => {
  event.preventDefault(); // Prevent default anchor behavior

  // Clear navigation state
  clearNavigationState();

  // Navigate to academy with force reload
  router.push({
    path: '/student/academy',
    query: { reload: Date.now() } // Add timestamp to force reload
  });

  // Close mobile menu
  mobileMenuOpen.value = false;
};

const navigateToClasses = (event) => {
  event.preventDefault(); // Prevent default anchor behavior

  // Clear navigation state
  clearNavigationState();

  // Log navigation attempt for debugging
  console.log('Navigating to classes page...');

  // Navigate to classes with force reload and name-based navigation
  router.push({
    name: 'AvailableClasses',
    query: { reload: Date.now() } // Add timestamp to force reload
  }).then(() => {
    console.log('Navigation to AvailableClasses successful');
  }).catch(err => {
    console.error('Navigation to AvailableClasses failed:', err);
    // Fallback to path-based navigation if name-based fails
    router.push('/student/classes');
  });

  // Close mobile menu
  mobileMenuOpen.value = false;
};

// Close mobile menu when window is resized to desktop size
const handleResize = () => {
  if (window.innerWidth >= 768 && mobileMenuOpen.value) {
    mobileMenuOpen.value = false;
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  window.addEventListener('click', handleClickOutside);

  // Initialize notifications
  loadNotifications();

  // Debug current route and active states
  console.log('Current route path:', route.path);
  console.log('isHomeActive:', isHomeActive.value);
  console.log('isAcademyActive:', isAcademyActive.value);
  console.log('isClassesActive:', isClassesActive.value);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('click', handleClickOutside);
});

// Watch for route changes to debug active states
watch(() => route.path, (newPath) => {
  console.log('=== NAVIGATION DEBUG ===');
  console.log('Route changed to:', newPath);
  console.log('Route query params:', route.query);
  console.log('isHomeActive:', isHomeActive.value);
  console.log('isAcademyActive:', isAcademyActive.value);
  console.log('isClassesActive:', isClassesActive.value);
  console.log('localStorage classSource:', localStorage.getItem(STORAGE_KEYS.CLASS_SOURCE));
  console.log('localStorage currentClassId:', localStorage.getItem(STORAGE_KEYS.CURRENT_CLASS_ID));
});

// Close mobile menu when sidebar is toggled
watch(isSidebarOpen, (newValue) => {
  if (newValue && mobileMenuOpen.value) {
    mobileMenuOpen.value = false;
  }
});

// Close notifications dropdown when sidebar is toggled
watch(isSidebarOpen, (newValue) => {
  if (newValue && showNotifications.value) {
    showNotifications.value = false;
  }
});

// Watch for changes in class data to update notifications
watch(() => classStore.classes.value, () => {
  // Regenerate notifications when class data changes
  generateNotificationsFromClasses();
  console.log('Notifications updated due to class data change');
}, { deep: true });

// Mark all notifications as read
const markAllAsRead = (event) => {
  if (event) {
    event.stopPropagation();
  }

  // Mark all notifications as read
  notifications.value.forEach(notification => {
    notification.read = true;
  });

  // Re-sort notifications after marking all as read
  // This is needed because we sort by read status
  resortNotifications();

  // Save to localStorage
  saveNotifications();

  console.log('All notifications marked as read');
};

// Resort notifications based on read status and timestamp
const resortNotifications = () => {
  notifications.value.sort((a, b) => {
    // First sort by read status (unread first)
    if (a.read !== b.read) {
      return a.read ? 1 : -1; // false comes before true
    }
    // Then sort by timestamp (newest first)
    return b.timestamp - a.timestamp;
  });
};

// Clear all notifications
const clearAllNotifications = (event) => {
  if (event) {
    event.stopPropagation();
  }

  // Clear all notifications
  notifications.value = [];

  // Save to localStorage
  saveNotifications();

  // Close dropdown after clearing
  showNotifications.value = false;

  console.log('All notifications cleared');
};

// Navigate to task detail when notification is clicked
const navigateToNotification = (notification) => {
  // Set current class and material
  classStore.setCurrentClass(notification.classId);
  classStore.setCurrentMaterial(notification.id);

  // Close notification dropdown
  showNotifications.value = false;

  // Mark notification as read
  const notificationIndex = notifications.value.findIndex(n => n.id === notification.id);
  if (notificationIndex !== -1) {
    notifications.value[notificationIndex].read = true;

    // Re-sort notifications after marking as read
    resortNotifications();

    // Save to localStorage
    saveNotifications();
  }

  // Determine appropriate navigation based on task status
  if (notification.taskStatus === 'pending' ||
      notification.taskStatus === 'past_due' ||
      notification.taskStatus === 'submitted' ||
      notification.taskStatus === 'under_review') {
    // For tasks that need action or are in progress, go to task detail
    router.push({
      name: 'DetailTaskHistory',
      params: {
        classId: notification.classId,
        materialId: notification.id
      },
      query: {
        source: 'notification'
      }
    });
  } else if (notification.isComplete) {
    // For completed materials, go to learning material detail
    router.push({
      name: 'DetailLearningMaterials',
      params: {
        classId: notification.classId,
        materialId: notification.id
      }
    });
  } else {
    // Default fallback to class detail
    router.push({
      name: 'DetailClass',
      params: {
        classId: notification.classId
      }
    });
  }

  console.log('Navigating to notification:', notification);
};
</script>

<script>
export default {
  name: 'NavbarStudent'
}
</script>
