export const programTypes = [
  { id: 'backend', name: 'Back End Development', icon: '/icons/server.svg' },
  { id: 'frontend', name: 'Front End Development', icon: '/icons/laptop.svg' },
  { id: 'mobile', name: 'Mobile App Development', icon: '/icons/phone.svg' }
];

export const mentors = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    role: 'Nest JS Specialist',
    image: '/testimonials/ellyMelly.png',
    bio: 'Experienced Nest.js developer with 8+ years in backend development. Specializes in building scalable microservices and RESTful APIs with TypeScript and Node.js.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 2,
    name: '<PERSON>',
    role: '<PERSON><PERSON> Expert',
    image: '/testimonials/micha<PERSON><PERSON><PERSON>.png',
    bio: 'Golang expert with experience at major tech companies. Passionate about teaching concurrent programming and building high-performance systems with Go.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Next.js Specialist',
    image: '/albert.png',
    bio: 'Next.js specialist with 6+ years of frontend development experience. Expert in server-side rendering, static site generation, and React ecosystem.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 4,
    name: 'Marvin McKinney',
    role: 'React.js Expert',
    image: '/marvin.png',
    bio: 'React.js developer with 7+ years of experience building modern web applications. Specializes in state management, hooks, and performance optimization.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 5,
    name: 'Jessica Park',
    role: 'Vue.js Specialist',
    image: '/testimonials/jessicaPark.png',
    bio: 'Vue.js expert with 5+ years of experience. Passionate about component-based architecture, Vuex state management, and building responsive UIs.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 6,
    name: 'David Wilson',
    role: 'Angular Expert',
    image: '/testimonials/davidWilson.png',
    bio: 'Angular developer with 6+ years of experience building enterprise applications. Expert in TypeScript, RxJS, and NgRx for state management.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 7,
    name: 'Sophia Rodriguez',
    role: 'Flutter Developer',
    image: '/testimonials/sophiaRodriguez.png',
    bio: 'Flutter developer with expertise in cross-platform mobile app development. Specializes in creating beautiful, responsive UIs with Dart programming language.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 8,
    name: 'Amanda Lee',
    role: 'Frontend Architect',
    image: '/testimonials/amandaLee.png',
    bio: 'Senior frontend architect with 10+ years of experience. Expert in modern JavaScript frameworks, responsive design, and performance optimization.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 9,
    name: 'Ethan Brown',
    role: 'Backend Engineer',
    image: '/testimonials/ethanBrown.png',
    bio: 'Backend engineer specializing in microservices architecture and cloud-native applications. Expert in Go, Node.js, and distributed systems.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 10,
    name: 'Sarah Chen',
    role: 'TypeScript Expert',
    image: '/testimonials/sarahChen.png',
    bio: 'TypeScript and Angular specialist with extensive experience in enterprise application development and team leadership.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 11,
    name: 'Ryan Zhang',
    role: 'Full Stack Developer',
    image: '/testimonials/ryanZhang.png',
    bio: 'Full stack developer with expertise in Vue.js, Node.js, and modern web technologies. Passionate about clean code and user experience.',
    socialLinks: { twitter: '#', linkedin: '#' }
  },
  {
    id: 12,
    name: 'Emily Davis',
    role: 'CSS Framework Specialist',
    image: '/avatar.jpg',
    bio: 'CSS framework specialist with deep knowledge of Tailwind CSS, responsive design, and modern styling techniques.',
    socialLinks: { twitter: '#', linkedin: '#' }
  }
];

export const programs = [
  {
    id: 1,
    type: 'frontend',
    title: 'Next Js',
    description: 'Build modern React applications with server-side rendering, static site generation, and more.',
    mentors: [
      { id: 3, name: 'Albert Flores', image: '/albert.png', price: 650000, duration: '4 Weeks' }
    ]
  },
  {
    id: 2,
    type: 'frontend',
    title: 'React Js',
    description: 'Master React and build interactive user interfaces with the most popular front-end library.',
    mentors: [
      { id: 4, name: 'Marvin McKinney', image: '/marvin.png', price: 600000, duration: '4 Weeks' }
    ]
  },
  {
    id: 3,
    type: 'frontend',
    title: 'Vue Js',
    description: 'Learn to build dynamic single-page applications with Vue.js, a progressive JavaScript framework.',
    mentors: [
      { id: 5, name: 'Jessica Park', image: '/testimonials/jessicaPark.png', price: 700000, duration: '5 Weeks' }
    ]
  },
  {
    id: 4,
    type: 'backend',
    title: 'Golang',
    description: "Master Google's programming language to build fast, reliable, and efficient back-end systems.",
    mentors: [
      { id: 2, name: 'Michael Johnson', image: '/testimonials/michaelJohnson.png', price: 500000, duration: '3 Weeks' }
    ]
  },
  {
    id: 5,
    type: 'backend',
    title: 'Node.js',
    description: 'Learn server-side JavaScript programming with Node.js to build scalable network applications.',
    mentors: [
      { id: 11, name: 'Ryan Zhang', image: '/testimonials/ryanZhang.png', price: 550000, duration: '4 Weeks' }
    ]
  },
  {
    id: 6,
    type: 'backend',
    title: 'Nest Js',
    description: 'Build scalable and maintainable server-side applications using the power of TypeScript and Node.js.',
    mentors: [
      { id: 1, name: 'Elly Melly', image: '/testimonials/ellyMelly.png', price: 600000, duration: '5 Weeks' }
    ]
  },
  {
    id: 7,
    type: 'frontend',
    title: 'Angular',
    description: 'Master Angular to build dynamic, single-page web applications with TypeScript.',
    mentors: [
      { id: 6, name: 'David Wilson', image: '/testimonials/davidWilson.png', price: 650000, duration: '4 Weeks' }
    ]
  },
  {
    id: 8,
    type: 'frontend',
    title: 'Tailwind CSS',
    description: 'Learn to rapidly build modern websites without ever leaving your HTML with this utility-first CSS framework.',
    mentors: [
      { id: 12, name: 'Emily Davis', image: '/avatar.jpg', price: 600000, duration: '3 Weeks' }
    ]
  },
  {
    id: 9,
    type: 'frontend',
    title: 'TypeScript',
    description: 'Add static typing to JavaScript to improve developer productivity and code quality.',
    mentors: [
      { id: 10, name: 'Sarah Chen', image: '/testimonials/sarahChen.png', price: 750000, duration: '6 Weeks' }
    ]
  },
  {
    id: 10,
    type: 'mobile',
    title: 'Flutter',
    description: 'Build beautiful, natively compiled applications for mobile, web, and desktop from a single codebase.',
    mentors: [
      { id: 7, name: 'Sophia Rodriguez', image: '/testimonials/sophiaRodriguez.png', price: 800000, duration: '8 Weeks' }
    ]
  },
  {
    id: 11,
    type: 'frontend',
    title: 'Svelte',
    description: 'Learn the modern JavaScript framework that compiles to vanilla JS for optimal performance.',
    mentors: [
      { id: 8, name: 'Amanda Lee', image: '/testimonials/amandaLee.png', price: 700000, duration: '5 Weeks' }
    ]
  },
  {
    id: 12,
    type: 'backend',
    title: 'Python Django',
    description: 'Build robust web applications with Python and the Django framework.',
    mentors: [
      { id: 9, name: 'Ethan Brown', image: '/testimonials/ethanBrown.png', price: 650000, duration: '6 Weeks' }
    ]
  }
];

export const testimonials = [
  {
    id: 1,
    name: 'Sarah Kristen',
    role: 'Student, National University',
    image: '/sarah.png',
    text: 'The Back-End Development Bootcamp was a game-changer for me! The mentors were incredibly supportive, and I learned Nest JS and Golang from scratch.',
    rating: 5,
    program: 'Back-End Development'
  },
  {
    id: 2,
    name: 'John Cooper',
    role: 'Student, Tech Institute',
    image: '/johncooper.png',
    text: 'Front-End bootcamp helped me transition from zero coding knowledge to a confident React developer. Amazing experience!',
    rating: 5,
    program: 'Front-End Development'
  },
  {
    id: 3,
    name: 'Maria Garcia',
    role: 'Student, Digital Academy',
    image: '/maria.png',
    text: 'The Mobile Development program exceeded my expectations. Now I can build professional Flutter applications with confidence.',
    rating: 5,
    program: 'Mobile Development'
  }
];

export const freeClasses = [
  {
    id: 1,
    title: 'Intro To Next JS: Build Modern Web Apps',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class1.png',
    type: 'frontend',
    mentorImage: '/theresa.png'
  },
  {
    id: 2,
    title: 'REST API With Golang: Create Scalable',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class2.png',
    type: 'backend',
    mentorImage: '/marvin.png'
  },
  {
    id: 3,
    title: 'Mobile App Development With Flutter',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class3.png',
    type: 'mobile',
    mentorImage: '/courtney.png'
  },
  {
    id: 4,
    title: 'Advanced Next JS Techniques',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class4.png',
    type: 'frontend',
    mentorImage: '/albert.png'
  },
  {
    id: 5,
    title: 'Building RESTful APIs with Go',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class5.png',
    type: 'backend',
    mentorImage: '/daniel.png'
  },
  {
    id: 6,
    title: 'Flutter UI Design Fundamentals',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class6.png',
    type: 'mobile',
    mentorImage: '/noela.png'
  },
  {
    id: 7,
    title: 'Optimizing Next JS Performance',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class7.png',
    type: 'frontend',
    mentorImage: '/queen.png'
  },
  {
    id: 8,
    title: 'Go Microservices Workshop',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class8.png',
    type: 'backend',
    mentorImage: '/reemar.png'
  },
  {
    id: 9,
    title: 'State Management in Flutter',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class9.png',
    type: 'mobile',
    mentorImage: '/maria.png'
  },
  {
    id: 10,
    title: 'Server-Side Rendering with Next JS',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class10.png',
    type: 'frontend',
    mentorImage: '/johncooper.png'
  },
  {
    id: 11,
    title: 'Testing Go Applications',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class11.png',
    type: 'backend',
    mentorImage: '/sarah.png'
  },
  {
    id: 12,
    title: 'Building Native Mobile Apps with Flutter',
    date: '18/02/2025',
    time: '12pm - 14pm',
    image: '/class12.png',
    type: 'mobile',
    mentorImage: '/albert.png'
  }
];

export const getProgramById = (id) => {
  return programs.find(program => program.id === parseInt(id));
};
