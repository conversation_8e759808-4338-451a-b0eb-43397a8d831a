export const testimonials = [
  // Backend Developer Testimonials
  {
    id: 1,
    name: '<PERSON><PERSON>',
    role: 'Backend Developer',
    image: '/testimonials/elly<PERSON>elly.png',
    feedback: 'The Back-End Development Bootcamp was a game-changer for me! The mentors were incredibly supportive, and I learned Nest JS and Golang from scratch. Now I work as a backend engineer at a fintech company.',
    rating: 5,
    category: 'backend'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Backend Engineer',
    image: '/albert.png',
    feedback: 'After completing the backend bootcamp, I gained solid skills in database design, API development with Node.js, and microservices architecture. The project-based learning approach made complex concepts easy to grasp.',
    rating: 5,
    category: 'backend'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Systems Architect',
    image: '/albert.png',
    feedback: 'The backend bootcamp helped me transition from a frontend role to a full-stack position. Learning about server optimization, Golang concurrency, and cloud deployment was exactly what I needed for my career growth.',
    rating: 5,
    category: 'backend'
  },

  // Frontend Developer Testimonials
  {
    id: 4,
    name: '<PERSON>',
    role: 'Frontend Developer',
    image: '/albert.png',
    feedback: 'This bootcamp gave me the confidence and skills to land my first job as a front-end developer! The hands-on projects with React and Vue.js were challenging but incredibly rewarding. The mentorship was a game-changer.',
    rating: 5,
    category: 'frontend'
  },
  {
    id: 5,
    name: 'David Wilson',
    role: 'UI/UX Engineer',
    image: '/albert.png',
    feedback: 'The frontend bootcamp transformed my design skills into practical development abilities. I learned modern JavaScript frameworks, responsive design techniques, and animation libraries that I use daily in my current role.',
    rating: 5,
    category: 'frontend'
  },
  {
    id: 6,
    name: 'Jessica Park',
    role: 'Frontend Specialist',
    image: '/albert.png',
    feedback: 'As someone with no prior coding experience, the frontend bootcamp provided the perfect foundation. From HTML/CSS fundamentals to advanced React patterns and state management with Redux, every lesson was valuable.',
    rating: 5,
    category: 'frontend'
  },

  // Mobile App Developer Testimonials
  {
    id: 7,
    name: 'Ryan Zhang',
    role: 'Mobile App Developer',
    image: '/albert.png',
    feedback: 'The mobile development bootcamp exceeded my expectations! I learned Flutter and was able to build cross-platform apps within weeks. The instructors were experts in the field and provided excellent guidance.',
    rating: 5,
    category: 'mobile'
  },
  {
    id: 8,
    name: 'Sophia Rodriguez',
    role: 'iOS Developer',
    image: '/albert.png',
    feedback: 'Transitioning from web to mobile development seemed daunting until I joined this bootcamp. The curriculum covered Swift, Kotlin, and React Native, giving me versatility in the job market. Now I work on apps used by thousands!',
    rating: 5,
    category: 'mobile'
  },
  {
    id: 9,
    name: 'Ethan Brown',
    role: 'Android Developer',
    image: '/albert.png',
    feedback: 'The mobile app bootcamp provided deep insights into native Android development with Kotlin. The focus on performance optimization and material design principles helped me create polished, professional apps from day one.',
    rating: 5,
    category: 'mobile'
  }
];