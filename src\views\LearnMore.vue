<template>
  <div>
    <!-- Hero Section -->
    <HeroSection
      badge-text="Bootcamp"
      title-text="Join Our Bootcamp and Shape Your Digital Future"
      subtitle-text="Boost Your Skills with Our Intensive Bootcamp — From Beginner to Job-Ready!"
      description-text="Ready to grow?"
      whatsappText="Try Free Consultation"
      :hero-image="headerImage"
      :use-overlay="true"
      :is-dark="true"
      :hide-right-section="true"
    />

    <!-- Section: Master Your Skills -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4 flex flex-col md:flex-row items-center gap-8">
        <div class="md:w-1/2 flex justify-center mb-8 md:mb-0">
          <img src="/LearnPic.png" alt="Master Your Skills" class="max-w-full h-auto" />
        </div>
        <div class="md:w-1/2">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">Master Your Skills with a Structured Bootcamp Curriculum</h2>
          <h3 class="text-xl font-bold mb-2">Comprehensive Curriculum With Hands-On Experience</h3>
          <p class="text-gray-600 mb-4">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
          <ul class="space-y-2">
            <li v-for="i in 4" :key="i" class="flex items-center text-gray-700">
              <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- What You'll Learn Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">What You'll Learn?</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Week 1 -->
          <div class="flex flex-col items-center text-center">
            <CircleWeek
              week="01"
              gradient-id="week1-gradient"
              :gradient-colors="['#FFB43A', '#FF5A1F']"
            />
            <h3 class="text-lg font-bold text-[#FFB43A] mb-2 mt-4">Introduction Nest JS</h3>
            <ul class="text-gray-700 text-sm list-decimal list-inside text-left">
              <li>Understanding Node.js and TypeScript basics</li>
              <li>Setting up a new Nest JS project</li>
              <li>Exploring the project structure (modules, controllers, services)</li>
            </ul>
          </div>
          <!-- Week 2 -->
          <div class="flex flex-col items-center text-center">
            <CircleWeek
              week="02"
              gradient-id="week2-gradient"
              :gradient-colors="['#FF9900', '#FF5A1F']"
            />
            <h3 class="text-lg font-bold text-[#FF9900] mb-2 mt-4">Building Rest APIs</h3>
            <ul class="text-gray-700 text-sm list-decimal list-inside text-left">
              <li>Defining routes and handling HTTP requests</li>
              <li>Implementing CRUD operations</li>
              <li>Testing API endpoints with Postman</li>
            </ul>
          </div>
          <!-- Week 3 -->
          <div class="flex flex-col items-center text-center">
            <CircleWeek
              week="03"
              gradient-id="week3-gradient"
              :gradient-colors="['#1CC8EE', '#007CF0']"
            />
            <h3 class="text-lg font-bold text-[#1CC8EE] mb-2 mt-4">Database Integration</h3>
            <ul class="text-gray-700 text-sm list-decimal list-inside text-left">
              <li>Introduction to databases SQL</li>
              <li>Connecting Nest JS to a PostgreSQL database using TypeORM</li>
              <li>Querying data</li>
            </ul>
          </div>
          <!-- Week 4 -->
          <div class="flex flex-col items-center text-center">
            <CircleWeek
              week="04"
              gradient-id="week4-gradient"
              :gradient-colors="['#FF5A5F', '#FFB43A']"
            />
            <h3 class="text-lg font-bold text-[#FF5A5F] mb-2 mt-4">Authentication</h3>
            <ul class="text-gray-700 text-sm list-decimal list-inside text-left">
              <li>Implementing JWT (JSON Web Token) authentication</li>
              <li>Creating login and registration endpoints</li>
              <li>Protecting routes with guards</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-4">
          <span class="text-teal-600 font-bold tracking-widest text-sm">PRICING</span>
        </div>
        <h2 class="text-4xl font-bold text-center mb-4">Affordable for you, valuable for your future</h2>
        <p class="text-gray-500 text-center mb-12 max-w-2xl mx-auto">Lorem ipsum dolor sit amet consectetur adipiscing elit dolor posuere vel venenatis eu sit massa volutpat.</p>
        <!-- Back to Program Button -->
        <div class="flex justify-start mb-6 max-w-4xl mx-auto">
          <router-link to="/program" class="inline-flex items-center text-gray-600 hover:text-gray-900 font-medium px-4 py-2 rounded transition">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Program
          </router-link>
        </div>
        <div class="flex flex-col md:flex-row items-center justify-center gap-8 max-w-4xl mx-auto bg-[#FAFAFD] rounded-2xl shadow p-8">
          <!-- Left: Info -->
          <div class="flex-1 flex flex-col justify-between items-start w-full md:w-1/2">
            <!-- NestJS Icon & Title -->
            <div class="flex items-center mb-2">
              <img src="/NestJS.png" alt="NestJS" class="w-10 h-10 mr-2" />
              <span class="text-2xl font-bold">Nest Js</span>
            </div>
            <p class="text-gray-500 mb-4">Learn from Patricia Vero, a Senior Back-End Developer at Google, and level up your skills with expert guidance.</p>
            <div class="mb-6">
              <span class="text-3xl font-bold text-black">Rp 600.000</span>
              <span class="text-gray-400 text-lg font-medium">/1 Month</span>
            </div>
            <button class="bg-[#006D77] hover:bg-[#00535a] text-white font-semibold rounded-lg px-8 py-3 text-lg shadow transition focus:outline-none focus:ring-2 focus:ring-[#006D77] active:scale-95">Book Class</button>
          </div>
          <!-- Right: Mentor Photo -->
          <div class="flex-1 flex flex-col items-center w-full md:w-1/2">
            <div class="relative w-full max-w-xs rounded-2xl overflow-hidden shadow mb-4">
              <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Patricia Vero" class="w-full h-56 object-cover" />
              <span class="absolute top-2 left-2 bg-black/60 text-white text-xs px-3 py-1 rounded-full">Patricia Vero</span>
            </div>
            <div class="flex items-center gap-2 bg-white rounded-full px-4 py-2 shadow text-xs font-medium">
              <span class="inline-flex items-center gap-1"><svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0z"/></svg> Senior Back-End Develop</span>
              <span class="inline-flex items-center gap-1"><img src="/WhiteGoogle.png" alt="Google" class="w-4 h-4 object-contain" /> Google</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { programs } from '@/data/programs';
import HeroSection from '@/components/HeroSection.vue';
import CircleWeek from '@/components/CircleWeek.vue';
const headerImage = '/header.jpg';

const route = useRoute();
const loading = ref(true);
const program = ref(null);

const currentMentor = computed(() => {
  if (!program.value) return null;
  
  if (program.value.mentors && Array.isArray(program.value.mentors)) {
    return program.value.mentors[0];
  } else if (program.value.mentor) {
    return {
      id: 1,
      name: program.value.mentor,
      image: program.value.mentorImage,
      price: program.value.price,
      duration: program.value.duration
    };
  }
  return null;
});

onMounted(() => {
  const programId = parseInt(route.params.id);
  program.value = programs.find(p => p.id === programId);
  loading.value = false;
});
</script>
  