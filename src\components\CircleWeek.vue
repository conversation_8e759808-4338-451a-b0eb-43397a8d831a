<template>
  <div class="relative flex items-center justify-center">
    <svg :width="size" :height="size" :viewBox="`0 0 ${size} ${size}`" fill="none">
      <!-- Gradient Defs -->
      <defs>
        <linearGradient :id="gradientId" x1="0" y1="0" :x2="size" :y2="size">
          <stop :offset="'0%'" :stop-color="gradientColors[0]" />
          <stop :offset="'100%'" :stop-color="gradientColors[1]" />
        </linearGradient>
      </defs>
      <!-- Outer Thin Gradient Circle -->
      <circle
        :cx="size/2"
        :cy="size/2"
        :r="outerThinRadius"
        :stroke="`url(#${gradientId})`"
        stroke-width="3"
        fill="none"
      />
      <!-- Main Gradient Circle -->
      <circle
        :cx="size/2"
        :cy="size/2"
        :r="outerRadius"
        :stroke="`url(#${gradientId})`"
        :stroke-width="strokeWidth"
        fill="none"
      />
      <!-- Left small circle (on outer thin circle) -->
      <circle
        :cx="size/2 - outerThinRadius * Math.cos(Math.PI/2)"
        :cy="size/2 + outerThinRadius * Math.sin(Math.PI/2)"
        :r="smallCircleRadius"
        fill="#fff"
        :stroke="`url(#${gradientId})`"
        stroke-width="3"
      />
      <!-- Right small circle (on outer thin circle) -->
      <circle
        :cx="size/2 + outerThinRadius * Math.cos(Math.PI/2)"
        :cy="size/2 - outerThinRadius * Math.sin(Math.PI/2)"
        :r="smallCircleRadius"
        fill="#fff"
        :stroke="`url(#${gradientId})`"
        stroke-width="3"
      />
    </svg>
    <!-- Centered Text -->
    <div class="absolute inset-0 flex flex-col items-center justify-center select-none">
      <span class="text-3xl md:text-4xl font-bold text-gray-900">{{ week }}</span>
      <span class="text-base md:text-lg text-gray-700 font-medium tracking-wide">WEEK</span>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
const props = defineProps({
  week: { type: [String, Number], required: true },
  gradientId: { type: String, required: true },
  gradientColors: { type: Array, required: true }, // [start, end]
  size: { type: Number, default: 140 },
  strokeWidth: { type: Number, default: 12 },
  smallCircleRadius: { type: Number, default: 10 },
});
const outerRadius = (props.size / 2) - (props.strokeWidth / 2) - 10;
const outerThinRadius = (props.size / 2) - (props.strokeWidth / 2);
</script>

<style scoped>
</style> 